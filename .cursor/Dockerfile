# Use a Node.js base image
FROM node:20-alpine

# Create a non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Set the working directory to the user's home
WORKDIR /home/<USER>

# Switch to non-root user
USER appuser

# Copy package.json and package-lock.json to install dependencies
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Expose the client and server ports
EXPOSE 3000
EXPOSE 5000

# Command to run the application in development mode
CMD ["npm", "run", "dev"]
