import React, { useState } from "react";
import AppLayout from "@/components/layout/AppLayout";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Search } from "lucide-react";

interface FAQItem {
  question: string;
  answer: React.ReactNode;
}

const FAQ_ITEMS: FAQItem[] = [
  {
    question: "What is ChewyAI?",
    answer:
      "<PERSON><PERSON><PERSON><PERSON><PERSON> is an online study assistant that uses artificial intelligence to turn your documents into helpful flashcards and quizzes so you can review information faster.",
  },
  {
    question: "How do I get started?",
    answer: (
      <ol className="list-decimal pl-4 space-y-1">
        <li>Create an account or sign in.</li>
        <li>On the Dashboard, choose "Add Document".</li>
        <li>Select a PDF, Word doc or text file to upload.</li>
        <li>When processing is done, open the document to generate flashcards or a quiz.</li>
      </ol>
    ),
  },
  {
    question: "What are the AI settings and how do I configure mine?",
    answer: (
      <ol className="list-decimal pl-4 space-y-1">
        <li>Open the "AI Settings" page from the sidebar or top menu.</li>
        <li>Select your preferred AI provider and model.</li>
        <li>Save your choices to control how your study material is processed.</li>
      </ol>
    ),
  },
  {
    question: "Can I use this on my phone?",
    answer:
      "Yes. Simply visit ChewyAI in your mobile browser and you can upload documents, create flashcards and take quizzes just like on a computer.",
  },
  {
    question: "How does AI Flashcards work?",
    answer: (
      <ol className="list-decimal pl-4 space-y-1">
        <li>Upload a document.</li>
        <li>Click "Generate Flashcards" next to that document.</li>
        <li>ChewyAI reads the text and creates a deck of cards.</li>
        <li>You can review each card and make edits before studying.</li>
      </ol>
    ),
  },
  {
    question: "What can I do with Flashcards?",
    answer:
      "Open the Flashcards page, pick a set and start reviewing. Mark cards you know, edit or add new cards, and track your progress as you study.",
  },
  {
    question: "How does the AI Quizzes feature work?",
    answer: (
      <ol className="list-decimal pl-4 space-y-1">
        <li>Select a document or flashcard set.</li>
        <li>Choose "Generate Quiz" and set your question style and amount.</li>
        <li>The AI builds a quiz from your material.</li>
        <li>Take the quiz to see what you have learned.</li>
      </ol>
    ),
  },
  {
    question: "What are the benefits of AI Quizzes?",
    answer:
      (
        <ul className="list-disc pl-4 space-y-1">
          <li>Pick question styles like multiple choice or short answer.</li>
          <li>Give the AI a prompt to focus on the topics you want.</li>
          <li>Decide how many questions to generate.</li>
          <li>Quickly check your understanding and spot areas to review.</li>
        </ul>
      ),
  },
  {
    question: "How do I delete a document?",
    answer:
      "Go to My Documents, click the three dots beside the file you no longer need and choose Delete Document, then confirm.",
  },
  {
    question: "How do I upload a document?",
    answer:
      "On the Dashboard or in the sidebar, click Add Document. Drag and drop or browse for a PDF, Word or text file. After the upload finishes, it will appear in your list ready to use.",
  },
];

const FAQPage: React.FC = () => {
  const [search, setSearch] = useState("");

  const filtered = FAQ_ITEMS.filter(
    (item) =>
      item.question.toLowerCase().includes(search.toLowerCase()) ||
      (typeof item.answer === "string" &&
        item.answer.toLowerCase().includes(search.toLowerCase()))
  );

  return (
    <AppLayout title="FAQ">
      <h1 className="text-2xl font-medium text-purple-400 mb-4">FAQ</h1>
      <div className="relative max-w-md mb-4">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400" size={18} />
        <Input
          type="text"
          placeholder="Search FAQs..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pl-9"
        />
      </div>
      <Separator className="my-4" />
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {filtered.map((faq, idx) => (
          <Dialog key={idx}>
            <DialogTrigger asChild>
              <Card className="p-4 cursor-pointer hover:bg-slate-700">
                {faq.question}
              </Card>
            </DialogTrigger>
            <DialogContent className="bg-card text-card-foreground border-border max-w-lg">
              <DialogHeader>
                <DialogTitle>{faq.question}</DialogTitle>
              </DialogHeader>
              <div className="mt-2 text-sm space-y-2">
                {faq.answer}
              </div>
            </DialogContent>
          </Dialog>
        ))}
      </div>
    </AppLayout>
  );
};

export default FAQPage;
