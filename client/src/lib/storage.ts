import { Flashcard, FlashcardDeck, Document, StudyStats } from "@/types";
import localforage from "localforage";
import axios from "axios";
import { API_BASE_URL } from "./config";

// Initialize localforage instances for different data types
const documentsStore = localforage.createInstance({ name: "documents" });
const decksStore = localforage.createInstance({ name: "flashcardDecks" });
const flashcardsStore = localforage.createInstance({ name: "flashcards" });

// Helper to get authorization token
const getAuthToken = async (): Promise<string | null> => {
  try {
    // Retrieve from localStorage or wherever your app stores the token
    return localStorage.getItem("auth_token");
  } catch (error) {
    console.error("Error getting auth token:", error);
    return null;
  }
};

// Helper to set up axios headers with auth token
const getAuthHeaders = async () => {
  const token = await getAuthToken();
  return (token && token.trim() !== '') ? { Authorization: `Bear<PERSON> ${token}` } : {};
};

// Document Local Storage Functions (for caching and offline support)
export async function cacheDocument(document: Document): Promise<Document> {
  await documentsStore.setItem(document.id, document);
  return document;
}

export async function getCachedDocument(id: string): Promise<Document | null> {
  return documentsStore.getItem<Document>(id);
}

export async function getCachedDocuments(): Promise<Document[]> {
  const documents: Document[] = [];
  await documentsStore.iterate<Document, void>((doc) => {
    documents.push(doc);
  });
  // Sort by creation date, newest first
  return documents.sort((a, b) => b.createdAt - a.createdAt);
}

export async function removeCachedDocument(id: string): Promise<void> {
  await documentsStore.removeItem(id);
}

export async function syncDocumentsToCache(
  documents: Document[]
): Promise<void> {
  for (const doc of documents) {
    await documentsStore.setItem(doc.id, doc);
  }
}

// Flashcard Decks API
export async function saveDeck(deck: FlashcardDeck): Promise<FlashcardDeck> {
  await decksStore.setItem(deck.id, deck);
  return deck;
}

export async function getDeck(id: string): Promise<FlashcardDeck | null> {
  return decksStore.getItem<FlashcardDeck>(id);
}

export async function getAllDecks(): Promise<FlashcardDeck[]> {
  const decks: FlashcardDeck[] = [];
  await decksStore.iterate<FlashcardDeck, void>((deck) => {
    decks.push(deck);
  });
  // Sort by creation date, newest first
  return decks.sort((a, b) => b.createdAt - a.createdAt);
}

export async function deleteDeck(id: string): Promise<void> {
  await decksStore.removeItem(id);

  // Also delete all flashcards in this deck
  const allCards = await getFlashcardsByDeck(id);
  for (const card of allCards) {
    await deleteFlashcard(card.id);
  }
}

export async function updateDeckStats(
  deckId: string
): Promise<FlashcardDeck | null> {
  const deck = await getDeck(deckId);
  if (!deck) return null;

  const cards = await getFlashcardsByDeck(deckId);
  const now = Date.now();

  const dueTodayCount = cards.filter((card) => {
    return card.nextReview ? card.nextReview <= now : false;
  }).length;

  const masteredCount = cards.filter((card) => {
    return card.interval && card.interval >= 30 * 24 * 60 * 60 * 1000; // 30 days
  }).length;

  const updatedDeck: FlashcardDeck = {
    ...deck,
    totalCards: cards.length,
    dueTodayCount,
    masteredCount,
  };

  return saveDeck(updatedDeck);
}

// Flashcards API
export async function saveFlashcard(card: Flashcard): Promise<Flashcard> {
  await flashcardsStore.setItem(card.id, card);
  await updateDeckStats(card.deckId);
  return card;
}

/**
 * Save a batch of flashcards and create/update a deck for them
 */
export async function saveFlashcards(
  cards: Flashcard[],
  documentId: string
): Promise<Flashcard[]> {
  if (!cards.length) return [];

  // Create a deck for these flashcards if it doesn't exist
  const deckId = crypto.randomUUID();
  const document = await getCachedDocument(documentId);
  const deckName = document
    ? `Flashcards: ${document.name}`
    : `Flashcards ${new Date().toLocaleDateString()}`;

  const deck: FlashcardDeck = {
    id: deckId,
    name: deckName,
    documentId,
    description: document
      ? `Generated from ${document.name}`
      : "Generated flashcards",
    createdAt: Date.now(),
    totalCards: cards.length,
    dueTodayCount: cards.length,
    masteredCount: 0,
  };

  await saveDeck(deck);

  // Save each flashcard with the deck ID
  const savedCards: Flashcard[] = [];
  for (const card of cards) {
    const flashcard: Flashcard = {
      ...card,
      deckId,
      createdAt: Date.now(),
    };
    await saveFlashcard(flashcard);
    savedCards.push(flashcard);
  }

  return savedCards;
}

export async function getFlashcard(id: string): Promise<Flashcard | null> {
  return flashcardsStore.getItem<Flashcard>(id);
}

export async function getFlashcardsByDeck(
  deckId: string
): Promise<Flashcard[]> {
  const cards: Flashcard[] = [];
  await flashcardsStore.iterate<Flashcard, void>((card) => {
    if (card.deckId === deckId) {
      cards.push(card);
    }
  });

  return cards;
}

export async function getDueFlashcards(deckId: string): Promise<Flashcard[]> {
  const now = Date.now();
  const cards = await getFlashcardsByDeck(deckId);
  return cards.filter((card) => {
    return !card.nextReview || card.nextReview <= now;
  });
}

export async function exportQuizzesAsJSON(): Promise<string> {
  const response = await fetch(`${API_BASE_URL}/quizzes/export?format=json`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to export quizzes');
  }

  return response.text();
}

export async function exportQuizzesAsCSV(): Promise<string> {
  const response = await fetch(`${API_BASE_URL}/quizzes/export?format=csv`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to export quizzes');
  }

  return response.text();
}

// Document comparison functions
export async function deleteFlashcard(id: string): Promise<void> {
  const card = await getFlashcard(id);
  if (card) {
    await flashcardsStore.removeItem(id);
    await updateDeckStats(card.deckId);
  }
}

// Study Statistics
export async function getStudyStats(): Promise<StudyStats> {
  const documents = await getCachedDocuments();
  const decks = await getAllDecks();

  let totalFlashcards = 0;
  let dueToday = 0;
  let totalCorrect = 0;
  let totalReviews = 0;

  for (const deck of decks) {
    totalFlashcards += deck.totalCards;
    dueToday += deck.dueTodayCount;

    const cards = await getFlashcardsByDeck(deck.id);
    for (const card of cards) {
      totalCorrect += card.correct || 0;
      totalReviews += (card.correct || 0) + (card.incorrect || 0);
    }
  }

  const accuracy = totalReviews > 0 ? (totalCorrect / totalReviews) * 100 : 0;

  return {
    totalDocuments: documents.length,
    totalFlashcards,
    dueToday,
    accuracy,
  };
}

// Export functionality
export async function exportFlashcardsAsJSON(): Promise<string> {
  const decks = await getAllDecks();
  const allData: {
    decks: FlashcardDeck[];
    flashcards: Record<string, Flashcard[]>;
  } = {
    decks: decks,
    flashcards: {},
  };

  for (const deck of decks) {
    const cards = await getFlashcardsByDeck(deck.id);
    allData.flashcards[deck.id] = cards;
  }

  return JSON.stringify(allData, null, 2);
}

export async function exportFlashcardsAsCSV(): Promise<string> {
  const decks = await getAllDecks();
  let csv =
    "Deck,Question,Answer,Created,Last Reviewed,Next Review,Interval,Easiness Factor\n";

  for (const deck of decks) {
    const cards = await getFlashcardsByDeck(deck.id);
    for (const card of cards) {
      const row = [
        `"${escapeCsvField(deck.name)}"`,
        `"${escapeCsvField(card.question)}"`,
        `"${escapeCsvField(card.answer)}"`,
        new Date(card.createdAt).toISOString(),
        card.lastReviewed ? new Date(card.lastReviewed).toISOString() : "",
        card.nextReview ? new Date(card.nextReview).toISOString() : "",
        card.interval ? Math.round(card.interval / (24 * 60 * 60 * 1000)) : "",
        card.easinessFactor || "",
      ];
      csv += row.join(",") + "\n";
    }
  }

  return csv;
}

function escapeCsvField(field: string): string {
  return field.replace(/"/g, '""');
}

// Clear all data
export async function clearAllData(): Promise<void> {
  await documentsStore.clear();
  await decksStore.clear();
  await flashcardsStore.clear();
}
