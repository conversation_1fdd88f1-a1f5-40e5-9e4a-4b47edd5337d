export interface AIProvider {
  name: string;
  baseUrl: string;
  apiKey: string;
  model: string;
}

export interface AIProviderSettings {
  provider: string;
  baseUrl: string;
  apiKey: string;
  /**
   * Model ID optimized for fast text extraction/summarization tasks.
   * Defaults to google/gemini-2.5-flash as per PRD.
   */
  extractionModel: string;
  /**
   * Model ID optimized for content generation tasks (e.g., flashcards, quizzes).
   * Defaults to google/gemini-2.5-pro-preview as per PRD.
   */
  generationModel: string;
  /**
   * Legacy field for backward-compatibility; mirrors generationModel.
   */
  model?: string;
}

export interface Flashcard {
  id: string;
  question: string;
  answer: string;
  deckId: string;
  createdAt: number;
  lastReviewed?: number;
  nextReview?: number;
  interval?: number;
  easinessFactor?: number;
  reviewCount?: number;
  correct?: number;
  incorrect?: number;
}

export interface FlashcardDeck {
  id: string;
  name: string;
  description?: string;
  documentId?: string;
  createdAt: number;
  totalCards: number;
  dueTodayCount: number;
  masteredCount: number;
}

export interface Document {
  id: string;
  name: string;
  content: string;
  type: string;
  createdAt: number;
  size: number;
}

export interface StudyStats {
  totalDocuments: number;
  totalFlashcards: number;
  dueToday: number;
  accuracy: number;
  totalQuizzes?: number;
  totalCompletions?: number;
}

export interface FlashcardGenerationRequest {
  content: string;
  documentName: string;
  count?: number;
}

export interface FlashcardGenerationResponse {
  cards: {
    question: string;
    answer: string;
  }[];
}
