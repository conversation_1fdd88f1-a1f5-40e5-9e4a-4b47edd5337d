import React from "react";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useAuth } from "../../hooks/useAuth";
import { <PERSON>, Settings, HelpCircle, Menu } from "lucide-react";
import { ThemeToggle } from "@/components/ui/ThemeToggle";

interface HeaderProps {
  toggleSidebar: () => void;
  title?: string;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar, title }) => {
  const { user } = useAuth();

  const getDisplayName = () => {
    if (user?.user_metadata?.full_name) {
      const nameParts = user.user_metadata.full_name.split(" ");
      if (nameParts.length > 1) {
        return `${nameParts[0][0]}${
          nameParts[nameParts.length - 1][0]
        }`.toUpperCase();
      }
      return nameParts[0];
    }
    if (user?.email) {
      return user.email.split("@")[0];
    }
    return "User";
  };

  const displayName = getDisplayName();
  const headerBgClass = "bg-slate-900";
  const headerTextClass = "text-slate-200";
  const headerHoverBgClass = "hover:bg-slate-700";
  const iconColorClass = "text-purple-400";
  const buttonTextHoverClass = "hover:text-purple-300";

  return (
    <header
      className={`${headerBgClass} ${headerTextClass} shadow-md sticky top-0 z-50`}
    >
      <div className="container mx-auto px-4 h-16 flex justify-between items-center">
        <div className="flex items-center">
          <Link
            href="/"
            className={`flex items-center space-x-2 group ${headerTextClass}`}
          >
            <Brain
              size={24}
              className={`${iconColorClass} group-hover:opacity-80 transition-opacity`}
            />
            <h1
              className={`text-xl font-semibold tracking-tight group-hover:opacity-80 transition-opacity ${buttonTextHoverClass}`}
            >
              ChewyAI
            </h1>
          </Link>
          {title && (
            <span className={`hidden md:block mx-3 ${headerTextClass}/60`}>
              |
            </span>
          )}
          {title && (
            <h2
              className={`hidden md:block text-lg font-medium ${headerTextClass}/80 truncate`}
            >
              {title}
            </h2>
          )}
        </div>

        <div className="flex items-center space-x-1">
          {user && (
            <div
              className={`hidden md:flex items-center space-x-2 mr-2 border-r border-slate-700 pr-3`}
            >
              <span className={`text-sm font-medium ${headerTextClass}/90`}>
                {user.user_metadata?.full_name || user.email}
              </span>
            </div>
          )}

          <TooltipProvider delayDuration={200}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="/ai-config">
                  <Button
                    variant="ghost"
                    className={`${headerTextClass} ${headerHoverBgClass} ${buttonTextHoverClass} active:bg-slate-600 px-2.5 py-1.5 text-sm`}
                  >
                    <Settings
                      size={18}
                      className={`mr-1.5 ${iconColorClass}`}
                    />
                    <span>Settings</span>
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent className="bg-slate-800 text-slate-200 border-slate-700">
                <p>Configure AI provider settings</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider delayDuration={200}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="/faq">
                  <Button
                    variant="ghost"
                    className={`${headerTextClass} ${headerHoverBgClass} ${buttonTextHoverClass} active:bg-slate-600 px-2.5 py-1.5 text-sm`}
                  >
                    <HelpCircle size={18} className={`mr-1.5 ${iconColorClass}`} />
                    <span>Help</span>
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent className="bg-slate-800 text-slate-200 border-slate-700">
                <p>View help documentation</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider delayDuration={200}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <ThemeToggle size="sm" variant="ghost" />
                </div>
              </TooltipTrigger>
              <TooltipContent className="bg-slate-800 text-slate-200 border-slate-700">
                <p>Toggle theme</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <Button
          variant="ghost"
          size="icon"
          className={`md:hidden ${headerTextClass} ${headerHoverBgClass} ${buttonTextHoverClass} active:bg-slate-600 focus:ring-2 focus:ring-purple-500/50`}
          onClick={toggleSidebar}
          aria-label="Toggle menu"
        >
          <Menu size={24} className={`${iconColorClass}`} />
        </Button>
      </div>
    </header>
  );
};

export default Header;
