{"name": "cline-evaluation-cli", "version": "0.1.0", "description": "CLI tool for orchestrating Cline evaluations across multiple benchmarks", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["cline", "evaluation", "benchmark"], "author": "", "license": "MIT", "dependencies": {"better-sqlite3": "^8.0.0", "chalk": "^4.1.2", "commander": "^9.4.1", "execa": "^5.1.1", "node-fetch": "^2.7.0", "ora": "^5.4.1", "sqlite": "^4.1.2", "uuid": "^9.0.0", "yargs": "^17.6.2"}, "devDependencies": {"@types/better-sqlite3": "^7.6.3", "@types/node": "^18.11.18", "@types/node-fetch": "^2.6.12", "@types/uuid": "^9.0.0", "@types/yargs": "^17.0.19", "ts-node": "^10.9.1", "typescript": "^4.9.4"}}