# This workflow will only label and/or close 30 issues at a time in order to avoid exceeding a rate limit.
# More info: https://docs.github.com/en/actions/use-cases-and-examples/project-management/closing-inactive-issues
name: Close inactive issues
on:
    schedule:
        - cron: "30 1 * * *"

jobs:
    close-issues:
        runs-on: ubuntu-latest
        permissions:
            issues: write
            pull-requests: write
        steps:
            - uses: actions/stale@28ca103
              with:
                  days-before-issue-stale: 60
                  days-before-issue-close: 14
                  stale-issue-label: "stale"
                  stale-issue-message: "This issue is stale because it has been open for 60 days with no activity."
                  close-issue-message: "This issue was closed because it has been inactive for 14 days since being marked as stale."
                  days-before-pr-stale: -1
                  days-before-pr-close: -1
                  exempt-issue-labels: "pinned,security"
                  repo-token: ${{ secrets.GITHUB_TOKEN }}
