### Description

<!-- Describe your changes in detail. What problem does this PR solve? -->

### Test Procedure

<!-- How did you test this? Are you confident that it will not introduce bugs? If so, why? -->

### Type of Change

<!-- Put an 'x' in all boxes that apply -->

-   [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
-   [ ] ✨ New feature (non-breaking change which adds functionality)
-   [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
-   [ ] ♻️ Refactor Changes
-   [ ] 💅 Cosmetic Changes
-   [ ] 📚 Documentation update
-   [ ] 🏃 Workflow Changes

### Pre-flight Checklist

<!-- Put an 'x' in all boxes that apply -->

-   [ ] Changes are limited to a single feature, bugfix or chore (split larger changes into separate PRs)
-   [ ] Tests are passing (`npm test`) and code is formatted and linted (`npm run format && npm run lint`)
-   [ ] I have created a changeset using `npm run changeset` (required for user-facing changes)
-   [ ] I have reviewed [contributor guidelines](https://github.com/cline/cline/blob/main/CONTRIBUTING.md)

### Screenshots

<!-- For UI changes, add screenshots here -->

### Additional Notes

<!-- Add any additional notes for reviewers -->
