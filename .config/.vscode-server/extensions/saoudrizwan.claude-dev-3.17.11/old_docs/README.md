# Cline Documentation

Welcome to the Cline documentation - your comprehensive guide to using and extending <PERSON><PERSON>'s capabilities. Here you'll find resources to help you get started, improve your skills, and contribute to the project.

## Getting Started

-   **New to coding?** We've prepared a gentle introduction:
    -   [Getting Started for New Coders](getting-started-new-coders/README.md)

## Improving Your Prompting Skills

-   **Want to communicate more effectively with C<PERSON>?** Explore:
    -   [Prompt Engineering Guide](prompting/README.md)
    -   [Cline Memory Bank](prompting/custom%20instructions%20library/cline-memory-bank.md)

## Exploring Cline's Tools

-   **Understand Cline's capabilities:**

    -   [Cline Tools Guide](tools/cline-tools-guide.md)
    -   [Mentions Feature Guide](tools/mentions-guide.md)

-   **Extend Cline with MCP Servers:**
    -   [MCP Overview](mcp/README.md)
    -   [Building MCP Servers from GitHub](mcp/mcp-server-from-github.md)
    -   [Building Custom MCP Servers](mcp/mcp-server-from-scratch.md)

## Contributing to <PERSON><PERSON>

-   **Interested in contributing?** We welcome your input:
    -   Feel free to submit a pull request
    -   [Contribution Guidelines](../CONTRIBUTING.md)

## Additional Resources

-   **Cline GitHub Repository:** [https://github.com/cline/cline](https://github.com/cline/cline)
-   **MCP Documentation:** [https://modelcontextprotocol.org/docs](https://modelcontextprotocol.org/docs)

We're always looking to improve this documentation. If you have suggestions or find areas that could be enhanced, please let us know. Your feedback helps make Cline better for everyone.
