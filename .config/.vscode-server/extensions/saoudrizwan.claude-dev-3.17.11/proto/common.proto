syntax = "proto3";

package cline;
option java_package = "bot.cline.proto";
option java_multiple_files = true;

message Metadata {
}

message EmptyRequest {
  Metadata metadata = 1;
}

message Empty {
}

message StringRequest {
  Metadata metadata = 1;
  string value = 2;
}

message StringArrayRequest {
  Metadata metadata = 1;
  repeated string value = 2;
}

message String {
  string value = 1;
}

message Int64Request {
  Metadata metadata = 1;
  int64 value = 2;
}

message Int64 {
  int64 value = 1;
}

message BytesRequest {
  Metadata metadata = 1;
  bytes value = 2;
}

message Bytes {
  bytes value = 1;
}

message BooleanRequest {
  Metadata metadata = 1;
  bool value = 2;
}

message Boolean {
  bool value = 1;
}

message StringArray {
  repeated string values = 1;
}

message StringArrays {
  repeated string values1 = 1;
  repeated string values2 = 2;
}
