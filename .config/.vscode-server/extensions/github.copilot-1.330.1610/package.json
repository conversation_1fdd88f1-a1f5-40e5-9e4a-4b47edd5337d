{"name": "copilot", "displayName": "GitHub Copilot", "description": "Your AI pair programmer", "version": "1.330.1610", "build": "1610", "buildType": "nightly", "publisher": "GitHub", "preview": false, "homepage": "https://github.com/features/copilot?editor=vscode", "license": "https://docs.github.com/en/site-policy/github-terms/github-terms-for-additional-products-and-features", "bugs": {"url": "https://github.com/microsoft/vscode-copilot-release/issues"}, "qna": "https://github.com/github-community/community/discussions/categories/copilot", "icon": "assets/Copilot-App-Icon.png", "pricing": "Trial", "extensionPack": ["GitHub.copilot-chat", "GitHub.copilot"], "engines": {"vscode": "^1.98.0", "node": ">=20.0.0", "npm": ">=9.0.0"}, "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Machine Learning"], "keywords": ["ai", "openai", "codex", "pilot", "snippets", "documentation", "autocomplete", "intellisense", "refactor", "javascript", "python", "typescript", "php", "go", "golang", "ruby", "c++", "c#", "java", "kotlin", "co-pilot"], "badges": [{"url": "https://img.shields.io/badge/GitHub%20Copilot-Subscription%20Required-orange", "href": "https://github.com/github-copilot/signup?editor=vscode", "description": "Sign up for GitHub Copilot"}, {"url": "https://img.shields.io/github/stars/github/copilot-docs?style=social", "href": "https://github.com/github/copilot-docs", "description": "Star Copilot on GitHub"}, {"url": "https://img.shields.io/youtube/channel/views/UC7c3Kb6jYCRj4JOHHZTxKsQ?style=social", "href": "https://www.youtube.com/@GitHub/search?query=copilot", "description": "Check out GitHub on Youtube"}, {"url": "https://img.shields.io/twitter/follow/github?style=social", "href": "https://twitter.com/github", "description": "Follow GitHub on Twitter"}], "activationEvents": ["onStartupFinished"], "browser": "./dist/web", "main": "./dist/extension", "enabledApiProposals": ["inlineCompletionsAdditions"], "contributes": {"commands": [{"command": "github.copilot.toggleStatusMenu", "title": "Open Status Menu", "category": "GitHub Copilot"}, {"command": "github.copilot.signIn", "title": "Sign In", "category": "GitHub Copilot", "enablement": "!github.copilot.activated"}, {"command": "github.copilot.acceptCursorPanelSolution", "title": "Accept Panel Suggestion at the Cursor", "enablement": "github.copilot.panelVisible", "category": "GitHub Copilot"}, {"command": "github.copilot.previousPanelSolution", "title": "Navigate to the Previous Panel Suggestion", "enablement": "github.copilot.panelVisible", "category": "GitHub Copilot"}, {"command": "github.copilot.nextPanelSolution", "title": "Navigate to the Next Panel Suggestion", "enablement": "github.copilot.panelVisible", "category": "GitHub Copilot"}, {"command": "github.copilot.generate", "title": "Open Completions Panel", "enablement": "github.copilot.activated && !isWeb", "category": "GitHub Copilot"}, {"command": "github.copilot.completions.disable", "title": "Disable Completions", "enablement": "github.copilot.activated && config.editor.inlineSuggest.enabled && github.copilot.completions.enabled", "category": "GitHub Copilot"}, {"command": "github.copilot.completions.enable", "title": "Enable Completions", "enablement": "github.copilot.activated && !(config.editor.inlineSuggest.enabled && github.copilot.completions.enabled)", "category": "GitHub Copilot"}, {"command": "github.copilot.completions.toggle", "title": "Toggle (Enable/Disable) Completions", "enablement": "github.copilot.activated", "category": "GitHub Copilot"}, {"command": "github.copilot.sendFeedback", "title": "Send Feedback", "category": "GitHub Copilot"}, {"command": "github.copilot.collectDiagnostics", "title": "Collect Diagnostics", "category": "GitHub Copilot", "enablement": "!isWeb"}, {"command": "github.copilot.openLogs", "title": "Open Logs", "category": "GitHub Copilot"}, {"command": "github.copilot.openModelPicker", "title": "Change Completions Model", "category": "GitHub Copilot", "enablement": "!isWeb"}, {"command": "github.copilot.sendCompletionFeedback", "title": "Send Copilot Completion Feedback", "category": "GitHub Copilot", "enablement": "!isWeb"}], "keybindings": [{"command": "github.copilot.acceptCursorPanelSolution", "key": "ctrl+/", "mac": "ctrl+/", "when": "activeWebviewPanelId == 'GitHub Copilot Suggestions'"}, {"command": "github.copilot.previousPanelSolution", "key": "alt+[", "mac": "alt+[", "when": "activeWebviewPanelId == 'GitHub Copilot Suggestions'"}, {"command": "github.copilot.nextPanelSolution", "key": "alt+]", "mac": "alt+]", "when": "activeWebviewPanelId == 'GitHub Copilot Suggestions'"}, {"command": "editor.action.inlineSuggest.trigger", "key": "alt+\\", "when": "editorTextFocus && !editorHasSelection && !inlineSuggestionsVisible"}], "configuration": [{"title": "GitHub Copilot", "properties": {"github.copilot.selectedCompletionModel": {"type": "string", "default": "", "markdownDescription": "The currently selected completion model ID. To select from a list of available models, use the __\"Change Completion Model\"__ command or open the model picker from the Copilot menu. The value must be a valid model ID. An empty value indicates that the default model will be used."}, "github.copilot.advanced": {"type": "object", "title": "Advanced Settings", "properties": {"authProvider": {"type": "string", "enum": ["github", "github-enterprise"], "enumDescriptions": ["GitHub.com", "GitHub Enterprise"], "default": "github", "description": "The GitHub identity to use for Copilot"}, "authPermissions": {"type": "string", "enum": ["default", "minimal"], "markdownEnumDescriptions": ["Default (recommended) - The default permissions enable the best that Copilot has to offer including, but not limited to, faster repo indexing and the power of the `@github` agent.", "Minimal - The minimal permissions required for Copilot functionality."], "default": "default", "markdownDescription": "Controls what kind of permissions are asked for when signing in to Copilot. The options are\n* `default` - (strongly recommended) The default permissions enable the best that Copilot has to offer including, but not limited to, faster repo indexing and the power of the `@github` agent.\n* `minimal` - The minimal permissions are the least that Copilot needs to function. Some features may behave slower or not at all."}, "useLanguageServer": {"type": "boolean", "default": false, "description": "Experimental: Use language server"}, "debug.overrideEngine": {"type": "string", "default": "", "description": "Override engine name"}, "debug.overrideProxyUrl": {"type": "string", "default": "", "description": "Override GitHub authentication proxy full URL"}, "debug.testOverrideProxyUrl": {"type": "string", "default": "", "description": "Override GitHub authentication proxy URL when running tests"}, "debug.overrideCapiUrl": {"type": "string", "default": "", "description": "Override GitHub Copilot API full URL"}, "debug.testOverrideCapiUrl": {"type": "string", "default": "", "description": "Override GitHub Copilot API URL when running tests"}, "debug.filterLogCategories": {"type": "array", "default": [], "deprecationMessage": "Set overrideLogLevels.* to ERROR to filter out unwanted categories.", "description": "Show only log categories listed in this setting. If an array is empty, show all loggers"}}}, "github.copilot.enable": {"type": "object", "scope": "window", "default": {"*": true, "plaintext": false, "markdown": false, "scminput": false}, "additionalProperties": {"type": "boolean"}, "markdownDescription": "Enable or disable Copilot completions for specified [languages](https://code.visualstudio.com/docs/languages/identifiers)"}, "github.copilot.inlineSuggest.enable": {"type": "boolean", "default": true, "deprecationMessage": "This setting has no effect. Please use github.copilot.enable instead.", "description": "Show inline suggestions"}, "github.copilot.editor.enableAutoCompletions": {"type": "boolean", "scope": "language-overridable", "default": true, "deprecationMessage": "This setting has no effect. Please use github.copilot.enable instead.", "description": "Automatically show inline completions"}}}], "configurationDefaults": {"editor.tokenColorCustomizations": {"[*Light*]": {"textMateRules": [{"scope": "ref.matchtext", "settings": {"foreground": "#000"}}]}, "[*Dark*]": {"textMateRules": [{"scope": "ref.matchtext", "settings": {"foreground": "#fff"}}]}}}, "languages": [{"id": "code-referencing"}], "grammars": [{"language": "code-referencing", "scopeName": "text.ref", "path": "./syntaxes/ref.tmGrammar.json"}], "iconFonts": [{"id": "copilot-font", "src": [{"path": "assets/copilot.woff", "format": "woff"}]}]}, "scripts": {"build": "tsx esbuild.ts", "clean": "./script/build/clean.sh", "compress": "tsx ./script/compressTokenizer.ts", "forbid-sources-content:extension": "node script/forbid-sources-content.js --extension", "generate_languages": "tsx script/generateLanguages.ts && prettier --write lib/src/language/generatedLanguages.ts", "get_token": "tsx script/getToken.ts", "lint": "run-p --aggregate-output \"lint:*\"", "lint:deps": "depcruise -c .dependency-cruiser.js .", "lint:eslint": "eslint -f visualstudio --quiet --cache .", "lint:prettier": "prettier --check . 2>&1", "lint:types": "tsc --noEmit && tsc --noEmit -p extension/src/copilotPanel/webview", "prebuild": "npm install", "pretest": "npm run build", "pretest:headless": "npm run build", "pretest:extension": "npm run build", "pretest:lsp-client": "npm run build", "pretest:lib-e2e": "npm run build", "prewatch": "npm run build", "prewatch:esbuild": "npm run build", "start": "npm run watch", "test": "npm-run-all \"test:extension --ignore-scripts\" \"test:headless --ignore-scripts\" lint", "test:headless": "npm-run-all test:lib test:agent \"test:lib-e2e --ignore-scripts\" test:prompt \"test:lsp-client --ignore-scripts\" lint", "test:agent": "mocha \"agent/src/**/*.test.{ts,tsx}\"", "test:extension": "tsx extension/test/runTest.ts", "test:lib": "mocha \"lib/src/**/*.test.{ts,tsx}\"", "test:lib-e2e": "mocha \"lib/e2e/src/**/*.test.{ts,tsx}\" --exclude \"lib/e2e/src/prompt/**/*.test.ts\"", "test:lib-e2e-no-ci": "mocha \"lib/e2e/no-ci/**/*.test.{ts,tsx}\"", "test:lib-prompt-e2e": "mocha \"lib/e2e/src/prompt/prompt.test.ts\"", "test:lib-prompt-e2e-perf": "INCLUDE_PERFORMANCE=true npm run test:lib-prompt-e2e", "test:lsp-client": "mocha \"lsp-client/test/*.test.{ts,tsx}\"", "test:prompt": "mocha \"prompt/test/**/*.test.{ts,tsx}\"", "vscode-dts": "vscode-dts dev && mv vscode.proposed.*.ts extension/src", "vscode:prepublish": "run-s \"build --ignore-scripts\" forbid-sources-content:extension", "vscode:uninstall": "node dist/extensionUninstalled.js", "vsix": "vsce package --allow-missing-repository", "watch": "run-p \"watch:esbuild --ignore-scripts\" \"watch:types -- --preserveWatchOutput\"", "watch:esbuild": "tsx esbuild.ts --watch", "watch:types": "tsc --noEmit --watch"}, "devDependencies": {"@datadog/datadog-ci": "^3.5.0", "@github/prettier-config": "0.0.6", "@limegrass/eslint-plugin-import-alias": "^1.5.1", "@parcel/watcher": "^2.5.1", "@types/benchmark": "^2.1.5", "@types/crypto-js": "^4.2.2", "@types/diff": "^7.0.2", "@types/git-url-parse": "^16.0.0", "@types/js-yaml": "^4.0.6", "@types/kerberos": "^1.1.2", "@types/mocha": "^10.0.10", "@types/node": "~20.8.0", "@types/semver": "^7.7.0", "@types/sinon": "^17.0.4", "@types/uuid": "^10.0.0", "@types/vscode": "1.98.0", "@types/yargs": "^17.0.24", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^3.3.2", "@yao-pkg/pkg": "^6.4.1", "benchmark": "^2.1.4", "boxen": "^8.0.1", "chalk": "^5.4.1", "dependency-cruiser": "^16.10.2", "electron": "^28.1.4", "esbuild": "^0.25.4", "esbuild-plugin-copy": "^2.1.1", "eslint": "^9.26.0", "eslint-formatter-visualstudio": "^8.40.0", "eslint-plugin-mocha": "^10.5.0", "fantasticon": "^3.0.0", "glob": "^11.0.2", "globals": "^16.1.0", "js-yaml": "^4.1.0", "mocha": "^11.2.2", "mocha-junit-reporter": "^2.2.1", "mocha-multi-reporters": "^1.5.1", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "proxy": "^2.1.1", "simple-git": "^3.27.0", "sinon": "^20.0.0", "ts-dedent": "^2.2.0", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vscode-dts": "^0.3.3"}, "dependencies": {"@adobe/helix-fetch": "github:devm33/helix-fetch#1088e599270f36632703f138d88c2100cbe468db", "@github/memoize": "1.1.5", "@microsoft/1ds-post-js": "^4.3.6", "@microsoft/applicationinsights-web-basic": "^3.3.6", "@microsoft/tiktokenizer": "^1.0.10", "@modelcontextprotocol/sdk": "^1.11.1", "@sinclair/typebox": "^0.34.33", "@types/vscode-webview": "^1.57.4", "@vscode/codicons": "^0.0.36", "@vscode/prompt-tsx": "0.4.0-alpha.1", "@vscode/webview-ui-toolkit": "^1.3.1", "await-lock": "^2.2.2", "crypto-js": "^4.2.0", "diff": "^7.0.0", "dldr": "^0.0.10", "get-stream": "^6.0.1", "git-url-parse": "^16.0.3", "kerberos": "^2.2.0", "mac-ca": "^3.1.1", "microjob": "^0.7.0", "minimatch": "^9.0.3", "open": "^10.1.2", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "semver": "^7.7.1", "shiki": "~1.15.0", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "vscode-languageclient": "^9.0.0", "vscode-languageserver": "^9.0.0", "vscode-languageserver-protocol": "^3.17", "vscode-languageserver-textdocument": "~1.0.11", "vscode-uri": "^3.1.0", "web-tree-sitter": "^0.20.8", "windows-ca-certs": "^0.1.0", "yargs": "^17.7.2"}, "overrides": {"fsevents": "<0", "bindings": "npm:bundled-bindings@^1.5.0"}, "__metadata": {"installedTimestamp": 1749251565016, "targetPlatform": "undefined", "size": 63025348}}