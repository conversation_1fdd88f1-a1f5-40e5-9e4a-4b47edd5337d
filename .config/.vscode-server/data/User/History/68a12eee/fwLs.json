{"name": "chewyai", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\" --names \"CLIENT,SERVER\" --prefix-colors \"cyan,yellow\"", "dev:client": "vite --port 3000", "dev:server": "tsx server/index.ts", "dev:frontend": "tsx frontend-server/index.ts", "preview:client": "vite preview --port 3000 --host", "build": "bash scripts/build-with-log.sh", "build:clean": "rm -rf dist", "build:client": "NODE_ENV=production vite build", "build:server": "esbuild server/index.ts --bundle --outdir=dist --platform=node --format=esm --packages=external --external:dotenv --external:lightningcss --external:@lightningcss/node --minify", "build:frontend": "esbuild frontend-server/index.ts --bundle --outfile=dist/frontend-server.js --platform=node --format=esm --packages=external --external:dotenv --minify", "start": "NODE_ENV=production concurrently \"node dist/index.js\" \"node dist/frontend-server.js\"", "start:frontend": "NODE_ENV=production node dist/frontend-server.js", "start:backend": "NODE_ENV=production node dist/index.js", "start:dev": "npm run dev", "preview": "npm run build && npm run start", "check": "tsc", "db:push": "drizzle-kit push", "test:build": "npm run build && echo 'Build completed successfully'", "test": "jest"}, "dependencies": {"@cyntler/react-doc-viewer": "^1.17.0", "@hono/node-server": "^1.14.1", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.60.5", "@types/jsonwebtoken": "^9.0.9", "@types/node-fetch": "^2.6.12", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "concurrently": "^9.1.0", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "express-validator": "^7.2.1", "framer-motion": "^11.13.1", "hono": "^4.7.9", "http-proxy-middleware": "^3.0.5", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "localforage": "^1.10.0", "lucide-react": "^0.453.0", "mammoth": "^1.9.0", "memorystore": "^1.6.7", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-local": "^1.0.0", "pdfjs-dist": "4.4.168", "postgres": "^3.4.5", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.6.0", "recharts": "^2.15.2", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.1.2", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.17", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.14", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/pg": "^8.15.2", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "jest": "^29.7.0", "postcss": "^8.4.47", "supertest": "^7.1.1", "tailwindcss": "^3.4.17", "ts-jest": "^29.3.4", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}