{"path": {"rootPath": "/home/<USER>/workspace", "relPath": ".replit"}, "originalCode": "modules = [\"nodejs-20\", \"web\"]\nrun = \"bash start-dev-clean.sh\"\n\nhidden = [\".config\", \".git\", \"generated-icon.png\", \"node_modules\", \"dist\"]\n\n[nix]\nchannel = \"stable-24_05\"\npackages = [\"supabase-cli\"]\n\n[env]\nPORT = \"5000\"\nFRONTEND_PORT = \"80\"\nVITE_API_BASE_URL = \"https://chewy-ai.replit.app:5000/api\"\n\n[deployment]\ndeploymentTarget = \"autoscale\"\nbuild = [\"sh\", \"-c\", \"npm run build\"]\nrun = [\"sh\", \"-c\", \"NODE_ENV=production npm run start\"]\nhealthcheck = \"/health\"\n\n[deployment.env]\nNODE_ENV = \"production\"\nPORT = \"5000\"\nFRONTEND_PORT = \"80\"\nBACKEND_URL = \"http://localhost:5000\"\nVITE_API_BASE_URL = \"https://chewy-ai.replit.app/api\"\n\n[[ports]]\nlocalPort = 3000\nexternalPort = 80\n\n[[ports]]\nlocalPort = 5000\nexternalPort = 5000\n\n[[ports]]\nlocalPort = 24678\n\n[workflows]\nrunButton = \"Project\"\n\n[[workflows.workflow]]\nname = \"Project\"\nmode = \"sequential\"\n\n[[workflows.workflow.tasks]]\ntask = \"shell.exec\"\nargs = \"bash start-dev-clean.sh\"\nwaitForPort = 5000\n", "modifiedCode": "modules = [\"nodejs-20\", \"web\"]\nrun = \"bash start-dev-clean.sh\"\n\nhidden = [\".config\", \".git\", \"generated-icon.png\", \"node_modules\", \"dist\"]\n\n[nix]\nchannel = \"stable-24_05\"\npackages = [\"supabase-cli\"]\n\n[env]\nPORT = \"5000\"\nFRONTEND_PORT = \"80\"\nVITE_API_BASE_URL = \"https://chewy-ai.replit.app:5000/api\"\n\n[deployment]\ndeploymentTarget = \"autoscale\"\nbuild = [\"sh\", \"-c\", \"npm run build\"]\nrun = [\"sh\", \"-c\", \"NODE_ENV=production npm run start\"]\nhealthcheck = \"/health\"\n\n[deployment.env]\nNODE_ENV = \"production\"\nPORT = \"5000\"\nFRONTEND_PORT = \"80\"\nBACKEND_URL = \"http://localhost:5000\"\nVITE_API_BASE_URL = \"https://chewy-ai.replit.app/api\"\n\n[[ports]]\nlocalPort = 80\nexternalPort = 80\n\n[[ports]]\nlocalPort = 5000\nexternalPort = 5000\n\n[[ports]]\nlocalPort = 24678\n\n[workflows]\nrunButton = \"Project\"\n\n[[workflows.workflow]]\nname = \"Project\"\nmode = \"sequential\"\n\n[[workflows.workflow.tasks]]\ntask = \"shell.exec\"\nargs = \"bash start-dev-clean.sh\"\nwaitForPort = 5000\n"}