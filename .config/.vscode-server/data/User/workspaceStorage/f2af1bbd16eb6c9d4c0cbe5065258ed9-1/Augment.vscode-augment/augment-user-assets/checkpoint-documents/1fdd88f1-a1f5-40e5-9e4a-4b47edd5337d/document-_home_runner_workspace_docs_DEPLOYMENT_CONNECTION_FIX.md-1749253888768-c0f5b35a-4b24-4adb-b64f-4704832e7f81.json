{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/DEPLOYMENT_CONNECTION_FIX.md"}, "modifiedCode": "# Deployment Connection Issue Fix\n\n## Problem Summary\n\nThe application was experiencing \"dial tcp 127.0.0.1:3000: connect: connection refused\" errors during deployment. The build process completed successfully, but the runtime proxy connections were failing.\n\n## Root Cause Analysis\n\n### 1. Backend URL Resolution Issue\n- **Problem**: Frontend server was incorrectly calculating backend URL\n- **Cause**: Logic in `frontend-server/index.ts` was using `process.env.PORT` (which is 5000) but defaulting to port 3000\n- **Result**: Frontend server tried to proxy to `http://localhost:3000` instead of `http://localhost:5000`\n\n### 2. Deployment Configuration Mismatch\n- **Problem**: `BACKEND_URL` in `.replit` was set to external URL `https://chewy-ai.replit.app:5000`\n- **Cause**: In containerized environments, services should communicate internally via localhost\n- **Result**: External URL caused connection issues in deployment environment\n\n### 3. Service Startup Order\n- **Problem**: Frontend server started before backend server\n- **Cause**: Concurrently command started frontend first\n- **Result**: Frontend server tried to connect to backend before it was ready\n\n## Implemented Fixes\n\n### 1. Enhanced Backend URL Resolution (`frontend-server/index.ts`)\n\n```typescript\n// Before (problematic)\nconst backendUrl = process.env.BACKEND_URL || `http://localhost:${process.env.PORT || \"5000\"}`;\n\n// After (fixed)\nlet backendUrl: string;\nif (process.env.BACKEND_URL) {\n  backendUrl = process.env.BACKEND_URL;\n} else if (process.env.NODE_ENV === \"production\") {\n  const backendPort = process.env.PORT || \"5000\";\n  backendUrl = `http://localhost:${backendPort}`;\n} else {\n  backendUrl = \"http://localhost:5000\";\n}\n```\n\n### 2. Updated Deployment Configuration (`.replit`)\n\n```toml\n# Before\nBACKEND_URL = \"https://chewy-ai.replit.app:5000\"\n\n# After\nBACKEND_URL = \"http://localhost:5000\"\n```\n\n### 3. Improved Error Handling and Logging\n\n- Added comprehensive error logging with environment details\n- Added backend connectivity check on startup\n- Enhanced proxy error messages with debugging information\n\n### 4. Fixed Service Startup Order\n\n```bash\n# Before\nconcurrently \"node dist/frontend-server.js\" \"node dist/index.js\"\n\n# After  \nconcurrently \"node dist/index.js\" \"node dist/frontend-server.js\"\n```\n\n## Environment Variable Configuration\n\n### Development\n```bash\nNODE_ENV=development\nPORT=5000\nFRONTEND_PORT=3000\n# BACKEND_URL not needed (defaults to http://localhost:5000)\n```\n\n### Production (Replit)\n```bash\nNODE_ENV=production\nPORT=5000\nFRONTEND_PORT=80\nBACKEND_URL=http://localhost:5000\nVITE_API_BASE_URL=https://chewy-ai.replit.app/api\n```\n\n## Verification Steps\n\n1. **Build Verification**\n   ```bash\n   npm run build\n   # Check that dist/index.js and dist/frontend-server.js exist\n   ```\n\n2. **Local Production Test**\n   ```bash\n   npm run preview\n   # Should start both servers without connection errors\n   ```\n\n3. **Health Check Verification**\n   ```bash\n   curl http://localhost:3000/health  # Frontend health\n   curl http://localhost:5000/api/health  # Backend health\n   ```\n\n## Monitoring and Debugging\n\n### Log Messages to Watch For\n\n**Success Indicators:**\n- `✅ Frontend server running on port 80`\n- `✅ Backend server running on port 5000`\n- `✅ Backend is reachable at http://localhost:5000`\n- `🔗 Proxying API requests to: http://localhost:5000`\n\n**Error Indicators:**\n- `❌ Proxy error: connect ECONNREFUSED`\n- `⚠️ Backend not immediately available`\n- `dial tcp 127.0.0.1:3000: connect: connection refused`\n\n### Debugging Commands\n\n```bash\n# Check running processes\nps aux | grep node\n\n# Check port usage\nnetstat -tulpn | grep :5000\nnetstat -tulpn | grep :80\n\n# Test backend directly\ncurl http://localhost:5000/api/health\n\n# Check environment variables\nenv | grep -E \"(NODE_ENV|PORT|BACKEND_URL)\"\n```\n\n## Prevention Strategies\n\n1. **Environment Variable Validation**: Added startup checks for required environment variables\n2. **Service Health Checks**: Added backend connectivity verification on frontend startup\n3. **Comprehensive Logging**: Enhanced error messages with environment context\n4. **Startup Order**: Ensured backend starts before frontend\n5. **Documentation**: Clear environment variable configuration guide\n\n## Related Files Modified\n\n- `frontend-server/index.ts` - Fixed backend URL resolution and added error handling\n- `.replit` - Updated deployment environment variables\n- `package.json` - Fixed service startup order\n- `scripts/start-production.sh` - Updated startup order\n- `scripts/dev-isolated.sh` - Updated startup order for consistency\n"}