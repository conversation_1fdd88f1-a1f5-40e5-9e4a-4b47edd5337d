{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/dev-isolated.sh"}, "originalCode": "#!/usr/bin/env bash\nset -euo pipefail\n\necho \"🚀 Starting ChewyAI in isolated development mode...\"\necho \"This mode runs the frontend server (port 3000) and backend server (port 5000) separately\"\necho \"Similar to production but with development builds\"\n\n# Check if build files exist\nif [ ! -d \"dist/public\" ]; then\n  echo \"❌ Frontend build not found. Building client...\"\n  npm run build:client\nfi\n\nif [ ! -f \"dist/frontend-server.js\" ]; then\n  echo \"❌ Frontend server build not found. Building frontend server...\"\n  npm run build:frontend\nfi\n\necho \"✅ All build files ready\"\n\n# Set development environment\nexport NODE_ENV=development\n\n# Start both servers with concurrently\necho \"🌐 Starting frontend server on port 3000...\"\necho \"🔧 Starting backend server on port 5000...\"\n\nconcurrently \\\n  --names \"FRONTEND,BACKEND\" \\\n  --prefix-colors \"cyan,green\" \\\n  --kill-others \\\n  \"npm run dev:frontend\" \\\n  \"npm run dev:server\"\n", "modifiedCode": "#!/usr/bin/env bash\nset -euo pipefail\n\necho \"🚀 Starting ChewyAI in isolated development mode...\"\necho \"This mode runs the frontend server (port 3000) and backend server (port 5000) separately\"\necho \"Similar to production but with development builds\"\n\n# Check if build files exist\nif [ ! -d \"dist/public\" ]; then\n  echo \"❌ Frontend build not found. Building client...\"\n  npm run build:client\nfi\n\nif [ ! -f \"dist/frontend-server.js\" ]; then\n  echo \"❌ Frontend server build not found. Building frontend server...\"\n  npm run build:frontend\nfi\n\necho \"✅ All build files ready\"\n\n# Set development environment\nexport NODE_ENV=development\n\n# Start both servers with concurrently\necho \"🔧 Starting backend server on port 5000...\"\necho \"🌐 Starting frontend server on port 3000...\"\n\nconcurrently \\\n  --names \"BACKEND,FRONTEND\" \\\n  --prefix-colors \"green,cyan\" \\\n  --kill-others \\\n  \"npm run dev:server\" \\\n  \"npm run dev:frontend\"\n"}