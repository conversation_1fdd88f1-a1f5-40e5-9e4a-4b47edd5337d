{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend-server/index.ts"}, "originalCode": "import express from \"express\";\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nimport cors from \"cors\";\nimport { createProxyMiddleware } from \"http-proxy-middleware\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\nconst app = express();\n\n// Load environment variables only in development\nif (process.env.NODE_ENV !== \"production\") {\n  const { default: dotenv } = await import(\"dotenv\");\n  dotenv.config({ path: path.resolve(__dirname, \"../.env\") });\n}\n\n// Security headers middleware\napp.use((req, res, next) => {\n  // Security headers\n  res.setHeader(\"X-Content-Type-Options\", \"nosniff\");\n  res.setHeader(\"X-Frame-Options\", \"DENY\");\n  res.setHeader(\"X-XSS-Protection\", \"1; mode=block\");\n  res.setHeader(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n  res.setHeader(\n    \"Content-Security-Policy\",\n    \"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://replit.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' ws: wss: http://localhost:5000 https:;\"\n  );\n  \n  next();\n});\n\n// CORS configuration for API requests to backend\napp.use(\n  cors({\n    origin: process.env.NODE_ENV === \"production\" \n      ? true \n      : [\"http://localhost:3000\", \"http://localhost:5000\"],\n    credentials: true,\n    methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\", \"OPTIONS\"],\n    allowedHeaders: [\"Content-Type\", \"Authorization\"],\n  })\n);\n\n// Parse JSON bodies\napp.use(express.json({ limit: \"10mb\" }));\napp.use(express.urlencoded({ extended: false }));\n\n// API proxy to backend server\n// Determine backend URL based on environment\nlet backendUrl: string;\n\nif (process.env.BACKEND_URL) {\n  // Use explicitly set backend URL (for deployment environments)\n  backendUrl = process.env.BACKEND_URL;\n} else if (process.env.NODE_ENV === \"production\") {\n  // In production without explicit BACKEND_URL, assume same-host communication\n  // Backend runs on PORT (default 5000), frontend runs on FRONTEND_PORT (default 3000)\n  const backendPort = process.env.PORT || \"5000\";\n  backendUrl = `http://localhost:${backendPort}`;\n} else {\n  // Development environment - backend typically on port 5000\n  backendUrl = \"http://localhost:5000\";\n}\n\nconsole.log(`🔗 Proxying API requests to: ${backendUrl}`);\nconsole.log(`📊 Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}, FRONTEND_PORT=${process.env.FRONTEND_PORT}`);\n\napp.use('/api', createProxyMiddleware({\n  target: backendUrl,\n  changeOrigin: true,\n  secure: false,\n  timeout: 30000,\n  proxyTimeout: 30000,\n  onError: (err, req, res) => {\n    console.error('❌ Proxy error:', err.message);\n    console.error('🔍 Target URL:', backendUrl);\n    console.error('🌐 Request URL:', req.url);\n    console.error('📊 Environment details:', {\n      NODE_ENV: process.env.NODE_ENV,\n      PORT: process.env.PORT,\n      FRONTEND_PORT: process.env.FRONTEND_PORT,\n      BACKEND_URL: process.env.BACKEND_URL\n    });\n    res.status(500).json({\n      error: 'Backend service unavailable',\n      message: 'Unable to connect to API server',\n      details: process.env.NODE_ENV === 'development' ? {\n        target: backendUrl,\n        error: err.message\n      } : undefined\n    });\n  },\n  onProxyReq: (proxyReq, req, res) => {\n    console.log(`[PROXY] ${req.method} ${req.url} -> ${backendUrl}${req.url}`);\n  },\n  onProxyRes: (proxyRes, req, res) => {\n    console.log(`[PROXY] ${req.method} ${req.url} <- ${proxyRes.statusCode}`);\n  }\n}));\n\n// Serve static files from the Vite build output\nconst distPath = path.resolve(__dirname, \"../dist/public\");\nconsole.log(`Serving static files from: ${distPath}`);\n\n// Static file serving with proper caching headers\napp.use(express.static(distPath, {\n  maxAge: process.env.NODE_ENV === \"production\" ? \"1y\" : \"0\",\n  etag: true,\n  lastModified: true,\n  setHeaders: (res, filePath) => {\n    // Cache static assets for a year in production\n    if (process.env.NODE_ENV === \"production\") {\n      if (filePath.match(/\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {\n        res.setHeader(\"Cache-Control\", \"public, max-age=31536000, immutable\");\n      } else if (filePath.endsWith(\"index.html\")) {\n        res.setHeader(\"Cache-Control\", \"no-cache, no-store, must-revalidate\");\n      }\n    }\n  }\n}));\n\n// Health check endpoint\napp.get(\"/health\", (req, res) => {\n  res.status(200).json({ \n    status: \"healthy\", \n    service: \"frontend-server\",\n    timestamp: new Date().toISOString(),\n    environment: process.env.NODE_ENV || \"development\"\n  });\n});\n\n// SPA fallback - serve index.html for all non-API routes\napp.get(\"*\", (req, res) => {\n  // API routes are handled by the proxy middleware above\n  // This fallback only handles frontend routes\n  const indexPath = path.join(distPath, \"index.html\");\n  res.sendFile(indexPath, (err) => {\n    if (err) {\n      console.error(\"Error serving index.html:\", err);\n      res.status(500).json({ error: \"Failed to serve application\" });\n    }\n  });\n});\n\n// Error handling middleware\napp.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {\n  console.error(\"Frontend server error:\", err);\n  res.status(500).json({ \n    error: \"Internal server error\",\n    message: process.env.NODE_ENV === \"development\" ? err.message : \"Something went wrong\"\n  });\n});\n\n// Function to check backend connectivity\nasync function checkBackendConnectivity(): Promise<boolean> {\n  try {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 5000);\n\n    const response = await fetch(`${backendUrl}/api/health`, {\n      method: 'GET',\n      signal: controller.signal\n    });\n\n    clearTimeout(timeoutId);\n    return response.ok;\n  } catch (error) {\n    console.warn(`⚠️  Backend not immediately available at ${backendUrl}/api/health:`, error instanceof Error ? error.message : 'Unknown error');\n    return false;\n  }\n}\n\n// Start the frontend server\nconst port = parseInt(process.env.FRONTEND_PORT || \"3000\", 10);\nconst host = \"0.0.0.0\";\n\napp.listen(port, host, async () => {\n  console.log(`✅ Frontend server running on port ${port}`);\n  console.log(`🌐 Frontend available at: http://localhost:${port}`);\n  console.log(`🔍 Frontend health check: http://localhost:${port}/health`);\n  console.log(`📁 Serving static files from: ${distPath}`);\n\n  // Check backend connectivity (non-blocking)\n  console.log(`🔍 Checking backend connectivity...`);\n  const backendAvailable = await checkBackendConnectivity();\n  if (backendAvailable) {\n    console.log(`✅ Backend is reachable at ${backendUrl}`);\n  } else {\n    console.log(`⚠️  Backend not immediately available at ${backendUrl}`);\n    console.log(`   This is normal if the backend is still starting up.`);\n    console.log(`   API requests will be retried automatically.`);\n  }\n});\n\n// Handle graceful shutdown\nconst signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];\nsignals.forEach((signal) => {\n  process.on(signal, () => {\n    console.log(`Frontend server received ${signal}, shutting down gracefully...`);\n    process.exit(0);\n  });\n});\n", "modifiedCode": "import express from \"express\";\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nimport cors from \"cors\";\nimport { createProxyMiddleware } from \"http-proxy-middleware\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\nconst app = express();\n\n// Load environment variables only in development\nif (process.env.NODE_ENV !== \"production\") {\n  const { default: dotenv } = await import(\"dotenv\");\n  dotenv.config({ path: path.resolve(__dirname, \"../.env\") });\n}\n\n// Security headers middleware\napp.use((req, res, next) => {\n  // Security headers\n  res.setHeader(\"X-Content-Type-Options\", \"nosniff\");\n  res.setHeader(\"X-Frame-Options\", \"DENY\");\n  res.setHeader(\"X-XSS-Protection\", \"1; mode=block\");\n  res.setHeader(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n  const cspPolicy = \"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://replit.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' ws: wss: http://localhost:5000 https:;\";\n  res.setHeader(\"Content-Security-Policy\", cspPolicy);\n\n  // Debug logging for CSP policy\n  if (process.env.NODE_ENV === \"production\") {\n    console.log(\"🔒 CSP Policy set:\", cspPolicy.includes(\"fonts.googleapis.com\") ? \"✅ Google Fonts allowed\" : \"❌ Google Fonts blocked\");\n  }\n  \n  next();\n});\n\n// CORS configuration for API requests to backend\napp.use(\n  cors({\n    origin: process.env.NODE_ENV === \"production\" \n      ? true \n      : [\"http://localhost:3000\", \"http://localhost:5000\"],\n    credentials: true,\n    methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\", \"OPTIONS\"],\n    allowedHeaders: [\"Content-Type\", \"Authorization\"],\n  })\n);\n\n// Parse JSON bodies\napp.use(express.json({ limit: \"10mb\" }));\napp.use(express.urlencoded({ extended: false }));\n\n// API proxy to backend server\n// Determine backend URL based on environment\nlet backendUrl: string;\n\nif (process.env.BACKEND_URL) {\n  // Use explicitly set backend URL (for deployment environments)\n  backendUrl = process.env.BACKEND_URL;\n} else if (process.env.NODE_ENV === \"production\") {\n  // In production without explicit BACKEND_URL, assume same-host communication\n  // Backend runs on PORT (default 5000), frontend runs on FRONTEND_PORT (default 3000)\n  const backendPort = process.env.PORT || \"5000\";\n  backendUrl = `http://localhost:${backendPort}`;\n} else {\n  // Development environment - backend typically on port 5000\n  backendUrl = \"http://localhost:5000\";\n}\n\nconsole.log(`🔗 Proxying API requests to: ${backendUrl}`);\nconsole.log(`📊 Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}, FRONTEND_PORT=${process.env.FRONTEND_PORT}`);\n\napp.use('/api', createProxyMiddleware({\n  target: backendUrl,\n  changeOrigin: true,\n  secure: false,\n  timeout: 30000,\n  proxyTimeout: 30000,\n  onError: (err, req, res) => {\n    console.error('❌ Proxy error:', err.message);\n    console.error('🔍 Target URL:', backendUrl);\n    console.error('🌐 Request URL:', req.url);\n    console.error('📊 Environment details:', {\n      NODE_ENV: process.env.NODE_ENV,\n      PORT: process.env.PORT,\n      FRONTEND_PORT: process.env.FRONTEND_PORT,\n      BACKEND_URL: process.env.BACKEND_URL\n    });\n    res.status(500).json({\n      error: 'Backend service unavailable',\n      message: 'Unable to connect to API server',\n      details: process.env.NODE_ENV === 'development' ? {\n        target: backendUrl,\n        error: err.message\n      } : undefined\n    });\n  },\n  onProxyReq: (proxyReq, req, res) => {\n    console.log(`[PROXY] ${req.method} ${req.url} -> ${backendUrl}${req.url}`);\n  },\n  onProxyRes: (proxyRes, req, res) => {\n    console.log(`[PROXY] ${req.method} ${req.url} <- ${proxyRes.statusCode}`);\n  }\n}));\n\n// Serve static files from the Vite build output\nconst distPath = path.resolve(__dirname, \"../dist/public\");\nconsole.log(`Serving static files from: ${distPath}`);\n\n// Static file serving with proper caching headers\napp.use(express.static(distPath, {\n  maxAge: process.env.NODE_ENV === \"production\" ? \"1y\" : \"0\",\n  etag: true,\n  lastModified: true,\n  setHeaders: (res, filePath) => {\n    // Cache static assets for a year in production\n    if (process.env.NODE_ENV === \"production\") {\n      if (filePath.match(/\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {\n        res.setHeader(\"Cache-Control\", \"public, max-age=31536000, immutable\");\n      } else if (filePath.endsWith(\"index.html\")) {\n        res.setHeader(\"Cache-Control\", \"no-cache, no-store, must-revalidate\");\n      }\n    }\n  }\n}));\n\n// Health check endpoint\napp.get(\"/health\", (req, res) => {\n  res.status(200).json({ \n    status: \"healthy\", \n    service: \"frontend-server\",\n    timestamp: new Date().toISOString(),\n    environment: process.env.NODE_ENV || \"development\"\n  });\n});\n\n// SPA fallback - serve index.html for all non-API routes\napp.get(\"*\", (req, res) => {\n  // API routes are handled by the proxy middleware above\n  // This fallback only handles frontend routes\n  const indexPath = path.join(distPath, \"index.html\");\n  res.sendFile(indexPath, (err) => {\n    if (err) {\n      console.error(\"Error serving index.html:\", err);\n      res.status(500).json({ error: \"Failed to serve application\" });\n    }\n  });\n});\n\n// Error handling middleware\napp.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {\n  console.error(\"Frontend server error:\", err);\n  res.status(500).json({ \n    error: \"Internal server error\",\n    message: process.env.NODE_ENV === \"development\" ? err.message : \"Something went wrong\"\n  });\n});\n\n// Function to check backend connectivity\nasync function checkBackendConnectivity(): Promise<boolean> {\n  try {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 5000);\n\n    const response = await fetch(`${backendUrl}/api/health`, {\n      method: 'GET',\n      signal: controller.signal\n    });\n\n    clearTimeout(timeoutId);\n    return response.ok;\n  } catch (error) {\n    console.warn(`⚠️  Backend not immediately available at ${backendUrl}/api/health:`, error instanceof Error ? error.message : 'Unknown error');\n    return false;\n  }\n}\n\n// Start the frontend server\nconst port = parseInt(process.env.FRONTEND_PORT || \"3000\", 10);\nconst host = \"0.0.0.0\";\n\napp.listen(port, host, async () => {\n  console.log(`✅ Frontend server running on port ${port}`);\n  console.log(`🌐 Frontend available at: http://localhost:${port}`);\n  console.log(`🔍 Frontend health check: http://localhost:${port}/health`);\n  console.log(`📁 Serving static files from: ${distPath}`);\n\n  // Check backend connectivity (non-blocking)\n  console.log(`🔍 Checking backend connectivity...`);\n  const backendAvailable = await checkBackendConnectivity();\n  if (backendAvailable) {\n    console.log(`✅ Backend is reachable at ${backendUrl}`);\n  } else {\n    console.log(`⚠️  Backend not immediately available at ${backendUrl}`);\n    console.log(`   This is normal if the backend is still starting up.`);\n    console.log(`   API requests will be retried automatically.`);\n  }\n});\n\n// Handle graceful shutdown\nconst signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];\nsignals.forEach((signal) => {\n  process.on(signal, () => {\n    console.log(`Frontend server received ${signal}, shutting down gracefully...`);\n    process.exit(0);\n  });\n});\n"}