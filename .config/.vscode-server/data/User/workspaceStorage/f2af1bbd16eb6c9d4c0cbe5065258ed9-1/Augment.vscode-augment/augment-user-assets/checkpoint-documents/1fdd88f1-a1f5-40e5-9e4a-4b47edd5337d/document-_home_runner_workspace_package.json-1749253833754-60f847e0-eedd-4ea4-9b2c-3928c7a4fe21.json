{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "package.json"}, "originalCode": "{\n  \"name\": \"chewyai\",\n  \"version\": \"1.0.0\",\n  \"type\": \"module\",\n  \"license\": \"MIT\",\n  \"scripts\": {\n    \"dev\": \"concurrently \\\"npm run dev:client\\\" \\\"npm run dev:server\\\" --names \\\"CLIENT,SERVER\\\" --prefix-colors \\\"cyan,yellow\\\"\",\n    \"dev:client\": \"vite --port 3000\",\n    \"dev:server\": \"tsx server/index.ts\",\n    \"dev:frontend\": \"tsx frontend-server/index.ts\",\n    \"preview:client\": \"vite preview --port 3000 --host\",\n    \"build\": \"bash scripts/build-with-log.sh\",\n    \"build:clean\": \"rm -rf dist\",\n    \"build:client\": \"NODE_ENV=production vite build\",\n    \"build:server\": \"esbuild server/index.ts --bundle --outdir=dist --platform=node --format=esm --packages=external --external:dotenv --external:lightningcss --external:@lightningcss/node --minify\",\n    \"build:frontend\": \"esbuild frontend-server/index.ts --bundle --outfile=dist/frontend-server.js --platform=node --format=esm --packages=external --external:dotenv --minify\",\n    \"start\": \"NODE_ENV=production concurrently \\\"node dist/frontend-server.js\\\" \\\"node dist/index.js\\\"\",\n    \"start:frontend\": \"NODE_ENV=production node dist/frontend-server.js\",\n    \"start:backend\": \"NODE_ENV=production node dist/index.js\",\n    \"start:dev\": \"npm run dev\",\n    \"preview\": \"npm run build && npm run start\",\n    \"check\": \"tsc\",\n    \"db:push\": \"drizzle-kit push\",\n    \"test:build\": \"npm run build && echo 'Build completed successfully'\",\n    \"test\": \"jest\"\n  },\n  \"dependencies\": {\n    \"@cyntler/react-doc-viewer\": \"^1.17.0\",\n    \"@hono/node-server\": \"^1.14.1\",\n    \"@hookform/resolvers\": \"^3.10.0\",\n    \"@jridgewell/trace-mapping\": \"^0.3.25\",\n    \"@neondatabase/serverless\": \"^0.10.4\",\n    \"@radix-ui/react-accordion\": \"^1.2.4\",\n    \"@radix-ui/react-alert-dialog\": \"^1.1.7\",\n    \"@radix-ui/react-aspect-ratio\": \"^1.1.3\",\n    \"@radix-ui/react-avatar\": \"^1.1.4\",\n    \"@radix-ui/react-checkbox\": \"^1.1.5\",\n    \"@radix-ui/react-collapsible\": \"^1.1.4\",\n    \"@radix-ui/react-context-menu\": \"^2.2.7\",\n    \"@radix-ui/react-dialog\": \"^1.1.7\",\n    \"@radix-ui/react-dropdown-menu\": \"^2.1.7\",\n    \"@radix-ui/react-hover-card\": \"^1.1.7\",\n    \"@radix-ui/react-label\": \"^2.1.3\",\n    \"@radix-ui/react-menubar\": \"^1.1.7\",\n    \"@radix-ui/react-navigation-menu\": \"^1.2.6\",\n    \"@radix-ui/react-popover\": \"^1.1.7\",\n    \"@radix-ui/react-progress\": \"^1.1.3\",\n    \"@radix-ui/react-radio-group\": \"^1.2.4\",\n    \"@radix-ui/react-scroll-area\": \"^1.2.4\",\n    \"@radix-ui/react-select\": \"^2.1.7\",\n    \"@radix-ui/react-separator\": \"^1.1.3\",\n    \"@radix-ui/react-slider\": \"^1.2.4\",\n    \"@radix-ui/react-slot\": \"^1.2.0\",\n    \"@radix-ui/react-switch\": \"^1.1.4\",\n    \"@radix-ui/react-tabs\": \"^1.1.4\",\n    \"@radix-ui/react-toast\": \"^1.2.7\",\n    \"@radix-ui/react-toggle\": \"^1.1.3\",\n    \"@radix-ui/react-toggle-group\": \"^1.1.3\",\n    \"@radix-ui/react-tooltip\": \"^1.2.0\",\n    \"@supabase/supabase-js\": \"^2.49.4\",\n    \"@tanstack/react-query\": \"^5.60.5\",\n    \"@types/jsonwebtoken\": \"^9.0.9\",\n    \"@types/node-fetch\": \"^2.6.12\",\n    \"axios\": \"^1.9.0\",\n    \"class-variance-authority\": \"^0.7.1\",\n    \"clsx\": \"^2.1.1\",\n    \"cmdk\": \"^1.1.1\",\n    \"concurrently\": \"^9.1.0\",\n    \"connect-pg-simple\": \"^10.0.0\",\n    \"cors\": \"^2.8.5\",\n    \"date-fns\": \"^3.6.0\",\n    \"dotenv\": \"^16.5.0\",\n    \"drizzle-orm\": \"^0.39.3\",\n    \"drizzle-zod\": \"^0.7.0\",\n    \"embla-carousel-react\": \"^8.6.0\",\n    \"express\": \"^4.21.2\",\n    \"express-session\": \"^1.18.1\",\n    \"express-validator\": \"^7.2.1\",\n    \"framer-motion\": \"^11.13.1\",\n    \"hono\": \"^4.7.9\",\n    \"http-proxy-middleware\": \"^3.0.5\",\n    \"input-otp\": \"^1.4.2\",\n    \"jsonwebtoken\": \"^9.0.2\",\n    \"localforage\": \"^1.10.0\",\n    \"lucide-react\": \"^0.453.0\",\n    \"mammoth\": \"^1.9.0\",\n    \"memorystore\": \"^1.6.7\",\n    \"next-themes\": \"^0.4.6\",\n    \"node-fetch\": \"^3.3.2\",\n    \"passport\": \"^0.7.0\",\n    \"passport-local\": \"^1.0.0\",\n    \"pdfjs-dist\": \"4.4.168\",\n    \"postgres\": \"^3.4.5\",\n    \"react\": \"^18.3.1\",\n    \"react-day-picker\": \"^8.10.1\",\n    \"react-dom\": \"^18.3.1\",\n    \"react-dropzone\": \"^14.3.8\",\n    \"react-hook-form\": \"^7.55.0\",\n    \"react-icons\": \"^5.4.0\",\n    \"react-markdown\": \"^10.1.0\",\n    \"react-pdf\": \"^9.2.1\",\n    \"react-resizable-panels\": \"^2.1.7\",\n    \"react-router-dom\": \"^7.6.0\",\n    \"recharts\": \"^2.15.2\",\n    \"rehype-highlight\": \"^7.0.2\",\n    \"rehype-raw\": \"^7.0.0\",\n    \"remark-gfm\": \"^4.0.1\",\n    \"tailwind-merge\": \"^2.6.0\",\n    \"tailwindcss-animate\": \"^1.0.7\",\n    \"tw-animate-css\": \"^1.2.5\",\n    \"uuid\": \"^11.1.0\",\n    \"vaul\": \"^1.1.2\",\n    \"wouter\": \"^3.3.5\",\n    \"ws\": \"^8.18.0\",\n    \"zod\": \"^3.24.2\",\n    \"zod-validation-error\": \"^3.4.0\"\n  },\n  \"devDependencies\": {\n    \"@replit/vite-plugin-cartographer\": \"^0.1.2\",\n    \"@replit/vite-plugin-runtime-error-modal\": \"^0.0.3\",\n    \"@tailwindcss/typography\": \"^0.5.15\",\n    \"@tailwindcss/vite\": \"^4.1.3\",\n    \"@types/connect-pg-simple\": \"^7.0.3\",\n    \"@types/cors\": \"^2.8.17\",\n    \"@types/express\": \"4.17.21\",\n    \"@types/express-session\": \"^1.18.0\",\n    \"@types/jest\": \"^29.5.14\",\n    \"@types/node\": \"20.16.11\",\n    \"@types/passport\": \"^1.0.16\",\n    \"@types/passport-local\": \"^1.0.38\",\n    \"@types/pg\": \"^8.15.2\",\n    \"@types/react\": \"^18.3.11\",\n    \"@types/react-dom\": \"^18.3.1\",\n    \"@types/supertest\": \"^6.0.3\",\n    \"@types/uuid\": \"^10.0.0\",\n    \"@types/ws\": \"^8.5.13\",\n    \"@vitejs/plugin-react\": \"^4.3.2\",\n    \"autoprefixer\": \"^10.4.20\",\n    \"drizzle-kit\": \"^0.30.4\",\n    \"esbuild\": \"^0.25.0\",\n    \"jest\": \"^29.7.0\",\n    \"postcss\": \"^8.4.47\",\n    \"supertest\": \"^7.1.1\",\n    \"tailwindcss\": \"^3.4.17\",\n    \"ts-jest\": \"^29.3.4\",\n    \"tsx\": \"^4.19.1\",\n    \"typescript\": \"5.6.3\",\n    \"vite\": \"^5.4.14\"\n  },\n  \"optionalDependencies\": {\n    \"bufferutil\": \"^4.0.8\"\n  }\n}\n", "modifiedCode": "{\n  \"name\": \"chewyai\",\n  \"version\": \"1.0.0\",\n  \"type\": \"module\",\n  \"license\": \"MIT\",\n  \"scripts\": {\n    \"dev\": \"concurrently \\\"npm run dev:client\\\" \\\"npm run dev:server\\\" --names \\\"CLIENT,SERVER\\\" --prefix-colors \\\"cyan,yellow\\\"\",\n    \"dev:client\": \"vite --port 3000\",\n    \"dev:server\": \"tsx server/index.ts\",\n    \"dev:frontend\": \"tsx frontend-server/index.ts\",\n    \"preview:client\": \"vite preview --port 3000 --host\",\n    \"build\": \"bash scripts/build-with-log.sh\",\n    \"build:clean\": \"rm -rf dist\",\n    \"build:client\": \"NODE_ENV=production vite build\",\n    \"build:server\": \"esbuild server/index.ts --bundle --outdir=dist --platform=node --format=esm --packages=external --external:dotenv --external:lightningcss --external:@lightningcss/node --minify\",\n    \"build:frontend\": \"esbuild frontend-server/index.ts --bundle --outfile=dist/frontend-server.js --platform=node --format=esm --packages=external --external:dotenv --minify\",\n    \"start\": \"NODE_ENV=production concurrently \\\"node dist/index.js\\\" \\\"node dist/frontend-server.js\\\"\",\n    \"start:frontend\": \"NODE_ENV=production node dist/frontend-server.js\",\n    \"start:backend\": \"NODE_ENV=production node dist/index.js\",\n    \"start:dev\": \"npm run dev\",\n    \"preview\": \"npm run build && npm run start\",\n    \"check\": \"tsc\",\n    \"db:push\": \"drizzle-kit push\",\n    \"test:build\": \"npm run build && echo 'Build completed successfully'\",\n    \"test\": \"jest\"\n  },\n  \"dependencies\": {\n    \"@cyntler/react-doc-viewer\": \"^1.17.0\",\n    \"@hono/node-server\": \"^1.14.1\",\n    \"@hookform/resolvers\": \"^3.10.0\",\n    \"@jridgewell/trace-mapping\": \"^0.3.25\",\n    \"@neondatabase/serverless\": \"^0.10.4\",\n    \"@radix-ui/react-accordion\": \"^1.2.4\",\n    \"@radix-ui/react-alert-dialog\": \"^1.1.7\",\n    \"@radix-ui/react-aspect-ratio\": \"^1.1.3\",\n    \"@radix-ui/react-avatar\": \"^1.1.4\",\n    \"@radix-ui/react-checkbox\": \"^1.1.5\",\n    \"@radix-ui/react-collapsible\": \"^1.1.4\",\n    \"@radix-ui/react-context-menu\": \"^2.2.7\",\n    \"@radix-ui/react-dialog\": \"^1.1.7\",\n    \"@radix-ui/react-dropdown-menu\": \"^2.1.7\",\n    \"@radix-ui/react-hover-card\": \"^1.1.7\",\n    \"@radix-ui/react-label\": \"^2.1.3\",\n    \"@radix-ui/react-menubar\": \"^1.1.7\",\n    \"@radix-ui/react-navigation-menu\": \"^1.2.6\",\n    \"@radix-ui/react-popover\": \"^1.1.7\",\n    \"@radix-ui/react-progress\": \"^1.1.3\",\n    \"@radix-ui/react-radio-group\": \"^1.2.4\",\n    \"@radix-ui/react-scroll-area\": \"^1.2.4\",\n    \"@radix-ui/react-select\": \"^2.1.7\",\n    \"@radix-ui/react-separator\": \"^1.1.3\",\n    \"@radix-ui/react-slider\": \"^1.2.4\",\n    \"@radix-ui/react-slot\": \"^1.2.0\",\n    \"@radix-ui/react-switch\": \"^1.1.4\",\n    \"@radix-ui/react-tabs\": \"^1.1.4\",\n    \"@radix-ui/react-toast\": \"^1.2.7\",\n    \"@radix-ui/react-toggle\": \"^1.1.3\",\n    \"@radix-ui/react-toggle-group\": \"^1.1.3\",\n    \"@radix-ui/react-tooltip\": \"^1.2.0\",\n    \"@supabase/supabase-js\": \"^2.49.4\",\n    \"@tanstack/react-query\": \"^5.60.5\",\n    \"@types/jsonwebtoken\": \"^9.0.9\",\n    \"@types/node-fetch\": \"^2.6.12\",\n    \"axios\": \"^1.9.0\",\n    \"class-variance-authority\": \"^0.7.1\",\n    \"clsx\": \"^2.1.1\",\n    \"cmdk\": \"^1.1.1\",\n    \"concurrently\": \"^9.1.0\",\n    \"connect-pg-simple\": \"^10.0.0\",\n    \"cors\": \"^2.8.5\",\n    \"date-fns\": \"^3.6.0\",\n    \"dotenv\": \"^16.5.0\",\n    \"drizzle-orm\": \"^0.39.3\",\n    \"drizzle-zod\": \"^0.7.0\",\n    \"embla-carousel-react\": \"^8.6.0\",\n    \"express\": \"^4.21.2\",\n    \"express-session\": \"^1.18.1\",\n    \"express-validator\": \"^7.2.1\",\n    \"framer-motion\": \"^11.13.1\",\n    \"hono\": \"^4.7.9\",\n    \"http-proxy-middleware\": \"^3.0.5\",\n    \"input-otp\": \"^1.4.2\",\n    \"jsonwebtoken\": \"^9.0.2\",\n    \"localforage\": \"^1.10.0\",\n    \"lucide-react\": \"^0.453.0\",\n    \"mammoth\": \"^1.9.0\",\n    \"memorystore\": \"^1.6.7\",\n    \"next-themes\": \"^0.4.6\",\n    \"node-fetch\": \"^3.3.2\",\n    \"passport\": \"^0.7.0\",\n    \"passport-local\": \"^1.0.0\",\n    \"pdfjs-dist\": \"4.4.168\",\n    \"postgres\": \"^3.4.5\",\n    \"react\": \"^18.3.1\",\n    \"react-day-picker\": \"^8.10.1\",\n    \"react-dom\": \"^18.3.1\",\n    \"react-dropzone\": \"^14.3.8\",\n    \"react-hook-form\": \"^7.55.0\",\n    \"react-icons\": \"^5.4.0\",\n    \"react-markdown\": \"^10.1.0\",\n    \"react-pdf\": \"^9.2.1\",\n    \"react-resizable-panels\": \"^2.1.7\",\n    \"react-router-dom\": \"^7.6.0\",\n    \"recharts\": \"^2.15.2\",\n    \"rehype-highlight\": \"^7.0.2\",\n    \"rehype-raw\": \"^7.0.0\",\n    \"remark-gfm\": \"^4.0.1\",\n    \"tailwind-merge\": \"^2.6.0\",\n    \"tailwindcss-animate\": \"^1.0.7\",\n    \"tw-animate-css\": \"^1.2.5\",\n    \"uuid\": \"^11.1.0\",\n    \"vaul\": \"^1.1.2\",\n    \"wouter\": \"^3.3.5\",\n    \"ws\": \"^8.18.0\",\n    \"zod\": \"^3.24.2\",\n    \"zod-validation-error\": \"^3.4.0\"\n  },\n  \"devDependencies\": {\n    \"@replit/vite-plugin-cartographer\": \"^0.1.2\",\n    \"@replit/vite-plugin-runtime-error-modal\": \"^0.0.3\",\n    \"@tailwindcss/typography\": \"^0.5.15\",\n    \"@tailwindcss/vite\": \"^4.1.3\",\n    \"@types/connect-pg-simple\": \"^7.0.3\",\n    \"@types/cors\": \"^2.8.17\",\n    \"@types/express\": \"4.17.21\",\n    \"@types/express-session\": \"^1.18.0\",\n    \"@types/jest\": \"^29.5.14\",\n    \"@types/node\": \"20.16.11\",\n    \"@types/passport\": \"^1.0.16\",\n    \"@types/passport-local\": \"^1.0.38\",\n    \"@types/pg\": \"^8.15.2\",\n    \"@types/react\": \"^18.3.11\",\n    \"@types/react-dom\": \"^18.3.1\",\n    \"@types/supertest\": \"^6.0.3\",\n    \"@types/uuid\": \"^10.0.0\",\n    \"@types/ws\": \"^8.5.13\",\n    \"@vitejs/plugin-react\": \"^4.3.2\",\n    \"autoprefixer\": \"^10.4.20\",\n    \"drizzle-kit\": \"^0.30.4\",\n    \"esbuild\": \"^0.25.0\",\n    \"jest\": \"^29.7.0\",\n    \"postcss\": \"^8.4.47\",\n    \"supertest\": \"^7.1.1\",\n    \"tailwindcss\": \"^3.4.17\",\n    \"ts-jest\": \"^29.3.4\",\n    \"tsx\": \"^4.19.1\",\n    \"typescript\": \"5.6.3\",\n    \"vite\": \"^5.4.14\"\n  },\n  \"optionalDependencies\": {\n    \"bufferutil\": \"^4.0.8\"\n  }\n}\n"}