{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/start-production.sh"}, "originalCode": "#!/usr/bin/env bash\nset -euo pipefail\n\necho \"🚀 Starting ChewyAI in production mode...\"\n\n# Check if build files exist\nif [ ! -f \"dist/index.js\" ]; then\n  echo \"❌ Backend build not found. Run 'npm run build' first.\"\n  exit 1\nfi\n\nif [ ! -f \"dist/frontend-server.js\" ]; then\n  echo \"❌ Frontend server build not found. Run 'npm run build' first.\"\n  exit 1\nfi\n\nif [ ! -d \"dist/public\" ]; then\n  echo \"❌ Frontend build not found. Run 'npm run build' first.\"\n  exit 1\nfi\n\necho \"✅ All build files found\"\n\n# Set production environment\nexport NODE_ENV=production\n\n# Start both servers with concurrently\necho \"🌐 Starting frontend server on port 3000...\"\necho \"🔧 Starting backend server on port 5000...\"\n\nconcurrently \\\n  --names \"FRONTEND,BACKEND\" \\\n  --prefix-colors \"cyan,green\" \\\n  --kill-others \\\n  \"node dist/frontend-server.js\" \\\n  \"node dist/index.js\"\n", "modifiedCode": "#!/usr/bin/env bash\nset -euo pipefail\n\necho \"🚀 Starting ChewyAI in production mode...\"\n\n# Check if build files exist\nif [ ! -f \"dist/index.js\" ]; then\n  echo \"❌ Backend build not found. Run 'npm run build' first.\"\n  exit 1\nfi\n\nif [ ! -f \"dist/frontend-server.js\" ]; then\n  echo \"❌ Frontend server build not found. Run 'npm run build' first.\"\n  exit 1\nfi\n\nif [ ! -d \"dist/public\" ]; then\n  echo \"❌ Frontend build not found. Run 'npm run build' first.\"\n  exit 1\nfi\n\necho \"✅ All build files found\"\n\n# Set production environment\nexport NODE_ENV=production\n\n# Start both servers with concurrently\necho \"🔧 Starting backend server on port 5000...\"\necho \"🌐 Starting frontend server on port 3000...\"\n\nconcurrently \\\n  --names \"BACKEND,FRONTEND\" \\\n  --prefix-colors \"green,cyan\" \\\n  --kill-others \\\n  \"node dist/index.js\" \\\n  \"node dist/frontend-server.js\"\n"}