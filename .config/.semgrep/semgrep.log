2025-06-06 23:12:26,744 - semgrep.run_scan - DEBUG - semgrep version 1.2.0
2025-06-06 23:12:26,749 - semgrep.config_resolver - DEBUG - Loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-06 23:12:26,867 - semgrep.config_resolver - DEBUG - Done loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-06 23:12:26,873 - semgrep.config_resolver - DEBUG - Saving rules to /tmp/semgrep-y8twx4sh.rules
2025-06-06 23:12:27,034 - semgrep.semgrep_core - DEBUG - Failed to open resource semgrep-core-proprietary: [Errno 2] No such file or directory: '/tmp/_MEIgZOH6x/semgrep/bin/semgrep-core-proprietary'.
2025-06-06 23:12:27,477 - semgrep.rule_lang - DEBUG - semgrep-core validation response: valid=True
2025-06-06 23:12:27,478 - semgrep.rule_lang - DEBUG - semgrep-core validation succeeded
2025-06-06 23:12:27,478 - semgrep.rule_lang - DEBUG - RPC validation succeeded
2025-06-06 23:12:27,478 - semgrep.config_resolver - DEBUG - loaded 1 configs in 0.7297606468200684
2025-06-06 23:12:27,546 - semgrep.run_scan - VERBOSE - running 712 rules from 1 config /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json_0
2025-06-06 23:12:27,546 - semgrep.run_scan - VERBOSE - No .semgrepignore found. Using default .semgrepignore rules. See the docs for the list of default ignores: https://semgrep.dev/docs/cli-usage/#ignore-files
2025-06-06 23:12:27,548 - semgrep.run_scan - VERBOSE - Rules:
2025-06-06 23:12:27,548 - semgrep.run_scan - VERBOSE - <SKIPPED DATA (too many entries; use --max-log-list-entries)>
2025-06-06 23:12:28,532 - semgrep.core_runner - DEBUG - Passing whole rules directly to semgrep_core
2025-06-06 23:12:28,830 - semgrep.core_runner - DEBUG - Running Semgrep engine with command:
2025-06-06 23:12:28,830 - semgrep.core_runner - DEBUG - /tmp/_MEIgZOH6x/semgrep/bin/opengrep-core -json -rules /tmp/tmprqeu7qi3.json -j 8 -targets /tmp/tmp4i67jvs_ -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
2025-06-06 23:13:33,478 - semgrep.core_runner - DEBUG - --- semgrep-core stderr ---
[00.09][[34mINFO[0m]: Executed as: /tmp/_MEIgZOH6x/semgrep/bin/opengrep-core -json -rules /tmp/tmprqeu7qi3.json -j 8 -targets /tmp/tmp4i67jvs_ -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
[00.09][[34mINFO[0m]: Version: 1.2.0
[00.09][[34mINFO[0m]: Parsing rules in /tmp/tmprqeu7qi3.json
[00.66][[34mINFO[0m]: scan: processing 657 files (skipping 0), with 454 rules (skipping 0 )
[04.49][[31mERROR[0m]: exception on server/debug/checkCredentials.ts (Unix_error: No such file or directory open server/debug/checkCredentials.ts)
[0m[04.49][[31mERROR[0m]: exception on server/debug/checkCredentials.ts (Unix_error: No such file or directory open server/debug/checkCredentials.ts)
[0m[04.50][[31mERROR[0m]: exception on server/debug/checkCredentials.ts (Unix_error: No such file or directory open server/debug/checkCredentials.ts)
[0m[05.06][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[05.43][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[05.93][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[06.05][[31mERROR[0m]: exception on server/routes/flashcards.ts (Unix_error: No such file or directory open server/routes/flashcards.ts)
[0m[06.05][[31mERROR[0m]: exception on server/routes/flashcards.ts (Unix_error: No such file or directory open server/routes/flashcards.ts)
[0m[06.05][[31mERROR[0m]: exception on server/routes/flashcards.ts (Unix_error: No such file or directory open server/routes/flashcards.ts)
[0m[07.22][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.res-render-injection file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[08.21][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-unsanitized-method file: supabase/functions/upload-study-document/index.ts func: ???]
[0m[09.01][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: supabase/functions/upload-study-document/index.ts func: ???]
[0m[09.05][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[09.83][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[10.25][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/InlineDocumentViewer.tsx func: ???]
[0m[11.29][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/dashboard/SRSDashboard.tsx func: ???]
[0m[11.69][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/dashboard/SRSDashboard.tsx func: ???]
[0m[17.27][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuizQuestionManager.tsx func: ???]
[0m[17.59][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuizQuestionManager.tsx func: ???]
[0m[17.66][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuizQuestionManager.tsx func: ???]
[0m[18.15][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[18.33][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[18.52][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[18.59][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[18.76][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[18.84][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[18.96][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[18.96][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[19.13][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[19.31][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[19.42][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[19.43][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[19.50][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[19.53][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[19.81][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/AiFlashcardGenerator.tsx func: ???]
[0m[19.86][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/components/quiz/QuizQuestionManager.tsx func: ???]
[0m[20.01][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[20.11][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/components/quiz/QuizQuestionManager.tsx func: ???]
[0m[20.17][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[20.25][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/components/quiz/QuizQuestionManager.tsx func: ???]
[0m[20.32][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/components/quiz/QuizQuestionManager.tsx func: ???]
[0m[20.33][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[21.23][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: supabase/functions/generate-quiz-questions/index.ts func: ???]
[0m[21.43][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: supabase/functions/generate-quiz-questions/index.ts func: ???]
[0m[21.56][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: supabase/functions/generate-quiz-questions/index.ts func: ???]
[0m[23.03][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[23.17][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[23.42][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[23.51][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[23.77][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[23.91][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[24.05][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[24.18][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[24.19][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/components/document/DocumentViewer.tsx func: ???]
[0m[24.89][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/FlashcardForm.tsx func: ???]
[0m[25.11][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/FlashcardForm.tsx func: ???]
[0m[25.11][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/FlashcardForm.tsx func: ???]
[0m[25.15][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/components/dashboard/StudySection.tsx func: ???]
[0m[25.30][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/components/dashboard/StudySection.tsx func: ???]
[0m[25.56][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/AiQuestionGenerator.tsx func: ???]
[0m[25.62][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/dashboard/StudySection.tsx func: ???]
[0m[25.65][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/FlashcardManager.tsx func: ???]
[0m[25.89][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/AiQuestionGenerator.tsx func: ???]
[0m[25.98][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/flashcards/FlashcardManager.tsx func: ???]
[0m[26.03][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/dashboard/StudySection.tsx func: ???]
[0m[26.10][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/EnhancedDocumentUpload.tsx func: ???]
[0m[26.33][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/document/EnhancedDocumentUpload.tsx func: ???]
[0m[27.48][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/quiz/CreateQuizForm.tsx func: ???]
[0m[27.65][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/components/dashboard/StudySection.tsx func: ???]
[0m[28.39][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/ai/AIConfigurationSection.tsx func: ???]
[0m[28.67][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/components/dashboard/StudySection.tsx func: ???]
[0m[28.73][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/components/flashcards/FlashcardManager.tsx func: ???]
[0m[29.10][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/components/flashcards/FlashcardManager.tsx func: ???]
[0m[33.98][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/documentRoutes.ts func: ???]
[0m[34.38][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/documentRoutes.ts func: ???]
[0m[34.97][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/documentRoutes.ts func: ???]
[0m[35.64][[31mERROR[0m]: exception on server/routes/quizExpressRoutes.ts (Unix_error: No such file or directory open server/routes/quizExpressRoutes.ts)
[0m[35.64][[31mERROR[0m]: exception on server/routes/quizExpressRoutes.ts (Unix_error: No such file or directory open server/routes/quizExpressRoutes.ts)
[0m[35.66][[31mERROR[0m]: exception on server/routes/quizExpressRoutes.ts (Unix_error: No such file or directory open server/routes/quizExpressRoutes.ts)
[0m[35.91][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/documentRoutes.ts func: ???]
[0m[37.15][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/documentRoutes.ts func: ???]
[0m[38.73][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes/documentRoutes.ts func: ???]
[0m[39.16][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: server/routes/flashcardSetRoutes.ts func: ???]
[0m[39.31][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/pages/DashboardPage.tsx func: ???]
[0m[39.52][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes/documentRoutes.ts func: ???]
[0m[39.84][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/DashboardPage.tsx func: ???]
[0m[39.90][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes/documentRoutes.ts func: ???]
[0m[40.55][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[40.61][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/flashcardSetRoutes.ts func: ???]
[0m[40.74][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[40.74][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[41.13][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[41.27][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[41.28][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[41.30][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.express-ssrf file: server/routes/documentRoutes.ts func: ???]
[0m[41.38][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.res-render-injection file: client/src/pages/DashboardPage.tsx func: ???]
[0m[41.64][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/documentRoutes.ts func: ???]
[0m[41.83][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/documentRoutes.ts func: ???]
[0m[42.36][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/documentRoutes.ts func: ???]
[0m[43.03][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[43.10][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/documentRoutes.ts func: ???]
[0m[43.27][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: client/src/pages/DashboardPage.tsx func: ???]
[0m[43.49][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/documentRoutes.ts func: ???]
[0m[43.56][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: server/routes/flashcardSetRoutes.ts func: ???]
[0m[43.82][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[43.93][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[44.01][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: server/routes/aiRoutes.ts func: ???]
[0m[44.18][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[44.36][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/aiRoutes.ts func: ???]
[0m[44.55][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-insecure-template-usage file: client/src/pages/DashboardPage.tsx func: ???]
[0m[44.67][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[44.80][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[45.07][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: server/routes/aiRoutes.ts func: ???]
[0m[45.32][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[45.60][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[45.66][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/aiRoutes.ts func: ???]
[0m[45.76][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[45.88][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes/aiRoutes.ts func: ???]
[0m[46.14][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/documentRoutes.ts func: ???]
[0m[46.76][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.express-ssrf file: server/routes/aiRoutes.ts func: ???]
[0m[47.09][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/documentRoutes.ts func: ???]
[0m[47.11][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/aiRoutes.ts func: ???]
[0m[47.29][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes/aiRoutes.ts func: ???]
[0m[47.31][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[47.42][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/documentRoutes.ts func: ???]
[0m[47.50][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/aiRoutes.ts func: ???]
[0m[47.65][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes/aiRoutes.ts func: ???]
[0m[47.69][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[47.86][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/aiRoutes.ts func: ???]
[0m[48.01][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[48.08][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes/aiRoutes.ts func: ???]
[0m[48.53][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/aiRoutes.ts func: ???]
[0m[48.54][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/dashboard/UploadSection.tsx func: ???]
[0m[48.71][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[48.79][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/aiRoutes.ts func: ???]
[0m[48.93][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[48.95][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/dashboard/UploadSection.tsx func: ???]
[0m[49.08][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[49.31][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/dashboard/UploadSection.tsx func: ???]
[0m[49.38][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes/documentRoutes.ts func: ???]
[0m[49.61][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/dashboard/UploadSection.tsx func: ???]
[0m[49.89][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuestionForm.tsx func: ???]
[0m[50.00][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuestionForm.tsx func: ???]
[0m[50.11][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/components/dashboard/UploadSection.tsx func: ???]
[0m[50.28][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/QuestionForm.tsx func: ???]
[0m[50.36][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[50.74][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[51.32][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/lib/api.ts func: getQuizByIdAPI:152459]
[0m[51.53][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[52.02][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: server/routes/quizRoutes.ts func: generateQuestionsFromAI:156698]
[0m[52.18][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: server/routes/quizRoutes.ts func: ???]
[0m[52.24][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[52.31][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: server/routes/quizRoutes.ts func: ???]
[0m[52.32][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.express-ssrf file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[52.50][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[52.54][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.res-render-injection file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[52.56][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: client/src/components/quiz/QuestionForm.tsx func: ???]
[0m[52.68][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[52.70][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: client/src/components/quiz/QuestionForm.tsx func: ???]
[0m[53.00][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[53.06][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: server/routes/quizRoutes.ts func: generateQuestionsFromAI:156698]
[0m[53.15][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[53.19][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[53.22][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: server/routes/quizRoutes.ts func: ???]
[0m[53.31][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[53.39][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[53.45][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/quizRoutes.ts func: generateQuestionsFromAI:156698]
[0m[53.45][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: client/src/components/dashboard/UploadSection.tsx func: ???]
[0m[53.45][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[53.59][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[53.71][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/quizRoutes.ts func: ???]
[0m[54.16][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[54.33][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: client/src/components/quiz/SRSQuizMode.tsx func: ???]
[0m[54.66][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: client/src/pages/FlashcardsPage.tsx func: ???]
[0m[54.86][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes/quizRoutes.ts func: ???]
[0m[60.35][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: server/routes/quizRoutes.ts func: generateQuestionsFromAI:156698]
[0m[60.93][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: server/routes/quizRoutes.ts func: ???]
[0m[60.98][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/quiz/QuizPlayer.tsx func: ???]
[0m[64.58][[34mINFO[0m]: Custom ignore pattern: None
[64.58][[34mINFO[0m]: Custom ignore pattern: None
--- end semgrep-core stderr ---
2025-06-06 23:13:33,580 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = dba75b85fc567707c179285ad63f98b7063f81ea873c69c1923f927f9a40e33d2ebca9d8fb2a4db3d5fc23dc43b616b68ac8d0a549f0b9ca1a8738022e8b7438_0
2025-06-06 23:13:33,581 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 90e85da291820bc77ea34f456a34bf3d1ac263ba77b46922c1d8937df89cab5d4f13c14b64fb6bd1b1bd5ce168892967385395d06e530bcb5a3b914077b30099_0
2025-06-06 23:13:33,582 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 90e85da291820bc77ea34f456a34bf3d1ac263ba77b46922c1d8937df89cab5d4f13c14b64fb6bd1b1bd5ce168892967385395d06e530bcb5a3b914077b30099_0
2025-06-06 23:13:33,582 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 90e85da291820bc77ea34f456a34bf3d1ac263ba77b46922c1d8937df89cab5d4f13c14b64fb6bd1b1bd5ce168892967385395d06e530bcb5a3b914077b30099_0
2025-06-06 23:13:33,583 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 358dbc04c40070c48e491d4cdfff2a60e49e9cd7ad59202b0fe88ac4c11c7e93dac50436800cb723701dac9f6c73c6aadbba5d1458ffc58df665830b0af90d37_0
2025-06-06 23:13:33,583 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 65d8818d9695321d8ee91046e0edf740176620a1e436c6e0664191c4d1a735c9db70df363c8f000098ef6aece04cc7745c56754cd4a4d44b751d5de2ec78ef43_0
2025-06-06 23:13:33,584 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 65d8818d9695321d8ee91046e0edf740176620a1e436c6e0664191c4d1a735c9db70df363c8f000098ef6aece04cc7745c56754cd4a4d44b751d5de2ec78ef43_0
2025-06-06 23:13:33,584 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('.env.example'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 65d8818d9695321d8ee91046e0edf740176620a1e436c6e0664191c4d1a735c9db70df363c8f000098ef6aece04cc7745c56754cd4a4d44b751d5de2ec78ef43_0
2025-06-06 23:13:33,585 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.generic-api-key') match_id = 78c6568e306f8a3a7c9517950b7dc28fd1010f213eaa6963912ae5dff4ffe490fff547ea81d13183c737745afa87eacb08f5946b6b3bcf33fac5d2067f617ab8_0
2025-06-06 23:13:33,586 - semgrep.rule_match - DEBUG - match_key = ('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 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 (?!(?i).*(client|endpoint|vpn|_ec2_|aws_|authorize|author|define|config|credential|setting|sample|xxxxxx|000000|buffer|delete|aaaaaa|fewfwef|getenv|env_|system|example|ecdsa|sha256|sha1|sha2|md5|alert|wizard|target|onboard|welcome|page|exploit|experiment|expire|rabbitmq|scraper|widget|music|dns_|dns-|yahoo|want|json|action|script|fix_|fix-|develop|compas|stripe|service|master|metric|tech|gitignore|rich|open|stack|irc_|irc-|sublime|kohana|has_|has-|fabric|wordpres|role|osx_|osx-|boost|addres|queue|working|sandbox|internet|print|vision|tracking|being|generator|traffic|world|pull|rust|watcher|small|auth|full|hash|more|install|auto|complete|learn|paper|installer|research|acces|last|binding|spine|into|chat|algorithm|resource|uploader|video|maker|next|proc|lock|robot|snake|patch|matrix|drill|terminal|term|stuff|genetic|generic|identity|audit|pattern|audio|web_|web-|crud|problem|statu|cms-|cms_|arch|coffee|workflow|changelog|another|uiview|content|kitchen|gnu_|gnu-|gnu\\.|conf|couchdb|client|opencv|rendering|update|concept|varnish|gui_|gui-|gui\\.|version|shared|extra|product|still|not_|not-|not\\.|drop|ring|png_|png-|png\\.|actively|import|output|backup|start|embedded|registry|pool|semantic|instagram|bash|system|ninja|drupal|jquery|polyfill|physic|league|guide|pack|synopsi|sketch|injection|svg_|svg-|svg\\.|friendly|wave|convert|manage|camera|link|slide|timer|wrapper|gallery|url_|url-|url\\.|todomvc|requirej|party|http|payment|async|library|home|coco|gaia|display|universal|func|metadata|hipchat|under|room|config|personal|realtime|resume|database|testing|tiny|basic|forum|meetup|yet_|yet-|yet\\.|cento|dead|fluentd|editor|utilitie|run_|run-|run\\.|box_|box-|box\\.|bot_|bot-|bot\\.|making|sample|group|monitor|ajax|parallel|cassandra|ultimate|site|get_|get-|get\\.|gen_|gen-|gen\\.|gem_|gem-|gem\\.|extended|image|knife|asset|nested|zero|plugin|bracket|mule|mozilla|number|act_|act-|act\\.|map_|map-|map\\.|micro|debug|openshift|chart|expres|backend|task|source|translate|jbos|composer|sqlite|profile|mustache|mqtt|yeoman|have|builder|smart|like|oauth|school|guideline|captcha|filter|bitcoin|bridge|color|toolbox|discovery|new_|new-|new\\.|dashboard|when|setting|level|post|standard|port|platform|yui_|yui-|yui\\.|grunt|animation|haskell|icon|latex|cheat|lua_|lua-|lua\\.|gulp|case|author|without|simulator|wifi|directory|lisp|list|flat|adventure|story|storm|gpu_|gpu-|gpu\\.|store|caching|attention|solr|logger|demo|shortener|hadoop|finder|phone|pipeline|range|textmate|showcase|app_|app-|app\\.|idiomatic|edit|our_|our-|our\\.|out_|out-|out\\.|sentiment|linked|why_|why-|why\\.|local|cube|gmail|job_|job-|job\\.|rpc_|rpc-|rpc\\.|contest|tcp_|tcp-|tcp\\.|usage|buildout|weather|transfer|automated|sphinx|issue|sas_|sas-|sas\\.|parallax|jasmine|addon|machine|solution|dsl_|dsl-|dsl\\.|episode|menu|theme|best|adapter|debugger|chrome|tutorial|life|step|people|joomla|paypal|developer|solver|team|current|love|visual|date|data|canva|container|future|xml_|xml-|xml\\.|twig|nagio|spatial|original|sync|archived|refinery|science|mapping|gitlab|play|ext_|ext-|ext\\.|session|impact|set_|set-|set\\.|see_|see-|see\\.|migration|commit|community|shopify|what\'|cucumber|statamic|mysql|location|tower|line|code|amqp|hello|send|index|high|notebook|alloy|python|field|document|soap|edition|email|php_|php-|php\\.|command|transport|official|upload|study|secure|angularj|akka|scalable|package|request|con_|con-|con\\.|flexible|security|comment|module|flask|graph|flash|apache|change|window|space|lambda|sheet|bookmark|carousel|friend|objective|jekyll|bootstrap|first|article|gwt_|gwt-|gwt\\.|classic|media|websocket|touch|desktop|real|read|recorder|moved|storage|validator|add-on|pusher|scs_|scs-|scs\\.|inline|asp_|asp-|asp\\.|timeline|base|encoding|ffmpeg|kindle|tinymce|pretty|jpa_|jpa-|jpa\\.|used|user|required|webhook|download|resque|espresso|cloud|mongo|benchmark|pure|cakephp|modx|mode|reactive|fuel|written|flickr|mail|brunch|meteor|dynamic|neo_|neo-|neo\\.|new_|new-|new\\.|net_|net-|net\\.|typo|type|keyboard|erlang|adobe|logging|ckeditor|message|iso_|iso-|iso\\.|hook|ldap|folder|reference|railscast|www_|www-|www\\.|tracker|azure|fork|form|digital|exporter|skin|string|template|designer|gollum|fluent|entity|language|alfred|summary|wiki|kernel|calendar|plupload|symfony|foundry|remote|talk|search|dev_|dev-|dev\\.|del_|del-|del\\.|token|idea|sencha|selector|interface|create|fun_|fun-|fun\\.|groovy|query|grail|red_|red-|red\\.|laravel|monkey|slack|supported|instant|value|center|latest|work|but_|but-|but\\.|bug_|bug-|bug\\.|virtual|tweet|statsd|studio|path|real-time|frontend|notifier|coding|tool|firmware|flow|random|mediawiki|bosh|been|beer|lightbox|theory|origin|redmine|hub_|hub-|hub\\.|require|pro_|pro-|pro\\.|ant_|ant-|ant\\.|any_|any-|any\\.|recipe|closure|mapper|event|todo|model|redi|provider|rvm_|rvm-|rvm\\.|program|memcached|rail|silex|foreman|activity|license|strategy|batch|streaming|fast|use_|use-|use\\.|usb_|usb-|usb\\.|impres|academy|slider|please|layer|cros|now_|now-|now\\.|miner|extension|own_|own-|own\\.|app_|app-|app\\.|debian|symphony|example|feature|serie|tree|project|runner|entry|leetcode|layout|webrtc|logic|login|worker|toolkit|mocha|support|back|inside|device|jenkin|contact|fake|awesome|ocaml|bit_|bit-|bit\\.|drive|screen|prototype|gist|binary|nosql|rest|overview|dart|dark|emac|mongoid|solarized|homepage|emulator|commander|django|yandex|gradle|xcode|writer|crm_|crm-|crm\\.|jade|startup|error|using|format|name|spring|parser|scratch|magic|try_|try-|try\\.|rack|directive|challenge|slim|counter|element|chosen|doc_|doc-|doc\\.|meta|should|button|packet|stream|hardware|android|infinite|password|software|ghost|xamarin|spec|chef|interview|hubot|mvc_|mvc-|mvc\\.|exercise|leaflet|launcher|air_|air-|air\\.|photo|board|boxen|way_|way-|way\\.|computing|welcome|notepad|portfolio|cat_|cat-|cat\\.|can_|can-|can\\.|magento|yaml|domain|card|yii_|yii-|yii\\.|checker|browser|upgrade|only|progres|aura|ruby_|ruby-|ruby\\.|polymer|util|lite|hackathon|rule|log_|log-|log\\.|opengl|stanford|skeleton|history|inspector|help|soon|selenium|lab_|lab-|lab\\.|scheme|schema|look|ready|leveldb|docker|game|minimal|logstash|messaging|within|heroku|mongodb|kata|suite|picker|win_|win-|win\\.|wip_|wip-|wip\\.|panel|started|starter|front-end|detector|deploy|editing|based|admin|capture|spree|page|bundle|goal|rpg_|rpg-|rpg\\.|setup|side|mean|reader|cookbook|mini|modern|seed|dom_|dom-|dom\\.|doc_|doc-|doc\\.|dot_|dot-|dot\\.|syntax|sugar|loader|website|make|kit_|kit-|kit\\.|protocol|human|daemon|golang|manager|countdown|connector|swagger|map_|map-|map\\.|mac_|mac-|mac\\.|man_|man-|man\\.|orm_|orm-|orm\\.|org_|org-|org\\.|little|zsh_|zsh-|zsh\\.|shop|show|workshop|money|grid|server|octopres|svn_|svn-|svn\\.|ember|embed|general|file|important|dropbox|portable|public|docpad|fish|sbt_|sbt-|sbt\\.|done|para|network|common|readme|popup|simple|purpose|mirror|single|cordova|exchange|object|design|gateway|account|lamp|intellij|math|mit_|mit-|mit\\.|control|enhanced|emitter|multi|add_|add-|add\\.|about|socket|preview|vagrant|cli_|cli-|cli\\.|powerful|top_|top-|top\\.|radio|watch|fluid|amazon|report|couchbase|automatic|detection|sprite|pyramid|portal|advanced|plu_|plu-|plu\\.|runtime|git_|git-|git\\.|uri_|uri-|uri\\.|haml|node|sql_|sql-|sql\\.|cool|core|obsolete|handler|iphone|extractor|array|copy|nlp_|nlp-|nlp\\.|reveal|pop_|pop-|pop\\.|engine|parse|check|html|nest|all_|all-|all\\.|chinese|buildpack|what|tag_|tag-|tag\\.|proxy|style|cookie|feed|restful|compiler|creating|prelude|context|java|rspec|mock|backbone|light|spotify|flex|related|shell|which|clas|webapp|swift|ansible|unity|console|tumblr|export|campfire|conway\'|made|riak|hero|here|unix|unit|glas|smtp|how_|how-|how\\.|hot_|hot-|hot\\.|debug|release|diff|player|easy|right|old_|old-|old\\.|animate|time|push|explorer|course|training|nette|router|draft|structure|note|salt|where|spark|trello|power|method|social|via_|via-|via\\.|vim_|vim-|vim\\.|select|webkit|github|ftp_|ftp-|ftp\\.|creator|mongoose|led_|led-|led\\.|movie|currently|pdf_|pdf-|pdf\\.|load|markdown|phalcon|input|custom|atom|oracle|phonegap|ubuntu|great|rdf_|rdf-|rdf\\.|popcorn|firefox|zip_|zip-|zip\\.|cuda|dotfile|static|openwrt|viewer|powered|graphic|les_|les-|les\\.|doe_|doe-|doe\\.|maven|word|eclipse|lab_|lab-|lab\\.|hacking|steam|analytic|option|abstract|archive|reality|switcher|club|write|kafka|arduino|angular|online|title|don\'t|contao|notice|analyzer|learning|zend|external|staging|busines|tdd_|tdd-|tdd\\.|scanner|building|snippet|modular|bower|stm_|stm-|stm\\.|lib_|lib-|lib\\.|alpha|mobile|clean|linux|nginx|manifest|some|raspberry|gnome|ide_|ide-|ide\\.|block|statistic|info|drag|youtube|koan|facebook|paperclip|art_|art-|art\\.|quality|tab_|tab-|tab\\.|need|dojo|shield|computer|stat|state|twitter|utility|converter|hosting|devise|liferay|updated|force|tip_|tip-|tip\\.|behavior|active|call|answer|deck|better|principle|ches|bar_|bar-|bar\\.|reddit|three|haxe|just|plug-in|agile|manual|tetri|super|beta|parsing|doctrine|minecraft|useful|perl|sharing|agent|switch|view|dash|channel|repo|pebble|profiler|warning|cluster|running|markup|evented|mod_|mod-|mod\\.|share|csv_|csv-|csv\\.|response|good|house|connect|built|build|find|ipython|webgl|big_|big-|big\\.|google|scala|sdl_|sdl-|sdl\\.|sdk_|sdk-|sdk\\.|native|day_|day-|day\\.|puppet|text|routing|helper|linkedin|crawler|host|guard|merchant|poker|over|writing|free|classe|component|craft|nodej|phoenix|longer|quick|lazy|memory|clone|hacker|middleman|factory|motion|multiple|tornado|hack|ssh_|ssh-|ssh\\.|review|vimrc|driver|driven|blog|particle|table|intro|importer|thrift|xmpp|framework|refresh|react|font|librarie|variou|formatter|analysi|karma|scroll|tut_|tut-|tut\\.|apple|tag_|tag-|tag\\.|tab_|tab-|tab\\.|category|ionic|cache|homebrew|reverse|english|getting|shipping|clojure|boot|book|branch|combination|combo)) 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 (?!(^0x0*|^pub)|.*\\.(bin|json|exe)$|.*(?i)(Client|Factory)$|(^__[A-Za-z]+__$)|^(12345|abcd)|^\\d+(\\.\\d+)?$) 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 entropy (?i)(?:key|api|token|secret|client|passwd|password|auth|access)(?:[0-9a-z\\-_\\t.]{0,20})(?:[\\s|\']|[\\s|"]){0,3}(?:=|>|:=|\\|\\|:|<=|=>|:)(?:\'|@\\"|\\"|\\s|=|\\x60){0,5}(?!([a-z]+\\.[a-zA-Z]+)|.*(\\d{4}-\\d{2}-\\d{2})|:*(?!("|\'))[0-9A-Za-z]+\\.[0-9A-Za-z]+,|[A-Z]+_[A-Z]+_)(?P<CONTENT>[0-9a-z\\-_.=\\~@]{10,150})(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$) (\\d\\.\\d\\.\\d-}|([\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})|(\\w)\\1{5}|(?i)keywords|xxxx|eeeeeeee|0000|\\*\\*\\*|example|test|author=|author("|\')|preview|[A-Z]+_KEY|[.]value|[.]key|-\\d\\.\\d\\.) (\\w|\\.)\\1{5} .*((?i)omitted|arn:aws|(?i)(pub.*key|public.*key)|(?i)clientToken|symbol|cache|author\\.).*', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.generic-api-key') match_id = e568a8303898f65c43738ce9b467b3811ade960877866255d500f56f4cf5486be06aa8df9e775fee32241e3dbf0b475534fcd8256ff0310b842e905efeecb8ee_0
2025-06-06 23:13:33,587 - semgrep.rule_match - DEBUG - match_key = ('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 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 (?!(?i).*(client|endpoint|vpn|_ec2_|aws_|authorize|author|define|config|credential|setting|sample|xxxxxx|000000|buffer|delete|aaaaaa|fewfwef|getenv|env_|system|example|ecdsa|sha256|sha1|sha2|md5|alert|wizard|target|onboard|welcome|page|exploit|experiment|expire|rabbitmq|scraper|widget|music|dns_|dns-|yahoo|want|json|action|script|fix_|fix-|develop|compas|stripe|service|master|metric|tech|gitignore|rich|open|stack|irc_|irc-|sublime|kohana|has_|has-|fabric|wordpres|role|osx_|osx-|boost|addres|queue|working|sandbox|internet|print|vision|tracking|being|generator|traffic|world|pull|rust|watcher|small|auth|full|hash|more|install|auto|complete|learn|paper|installer|research|acces|last|binding|spine|into|chat|algorithm|resource|uploader|video|maker|next|proc|lock|robot|snake|patch|matrix|drill|terminal|term|stuff|genetic|generic|identity|audit|pattern|audio|web_|web-|crud|problem|statu|cms-|cms_|arch|coffee|workflow|changelog|another|uiview|content|kitchen|gnu_|gnu-|gnu\\.|conf|couchdb|client|opencv|rendering|update|concept|varnish|gui_|gui-|gui\\.|version|shared|extra|product|still|not_|not-|not\\.|drop|ring|png_|png-|png\\.|actively|import|output|backup|start|embedded|registry|pool|semantic|instagram|bash|system|ninja|drupal|jquery|polyfill|physic|league|guide|pack|synopsi|sketch|injection|svg_|svg-|svg\\.|friendly|wave|convert|manage|camera|link|slide|timer|wrapper|gallery|url_|url-|url\\.|todomvc|requirej|party|http|payment|async|library|home|coco|gaia|display|universal|func|metadata|hipchat|under|room|config|personal|realtime|resume|database|testing|tiny|basic|forum|meetup|yet_|yet-|yet\\.|cento|dead|fluentd|editor|utilitie|run_|run-|run\\.|box_|box-|box\\.|bot_|bot-|bot\\.|making|sample|group|monitor|ajax|parallel|cassandra|ultimate|site|get_|get-|get\\.|gen_|gen-|gen\\.|gem_|gem-|gem\\.|extended|image|knife|asset|nested|zero|plugin|bracket|mule|mozilla|number|act_|act-|act\\.|map_|map-|map\\.|micro|debug|openshift|chart|expres|backend|task|source|translate|jbos|composer|sqlite|profile|mustache|mqtt|yeoman|have|builder|smart|like|oauth|school|guideline|captcha|filter|bitcoin|bridge|color|toolbox|discovery|new_|new-|new\\.|dashboard|when|setting|level|post|standard|port|platform|yui_|yui-|yui\\.|grunt|animation|haskell|icon|latex|cheat|lua_|lua-|lua\\.|gulp|case|author|without|simulator|wifi|directory|lisp|list|flat|adventure|story|storm|gpu_|gpu-|gpu\\.|store|caching|attention|solr|logger|demo|shortener|hadoop|finder|phone|pipeline|range|textmate|showcase|app_|app-|app\\.|idiomatic|edit|our_|our-|our\\.|out_|out-|out\\.|sentiment|linked|why_|why-|why\\.|local|cube|gmail|job_|job-|job\\.|rpc_|rpc-|rpc\\.|contest|tcp_|tcp-|tcp\\.|usage|buildout|weather|transfer|automated|sphinx|issue|sas_|sas-|sas\\.|parallax|jasmine|addon|machine|solution|dsl_|dsl-|dsl\\.|episode|menu|theme|best|adapter|debugger|chrome|tutorial|life|step|people|joomla|paypal|developer|solver|team|current|love|visual|date|data|canva|container|future|xml_|xml-|xml\\.|twig|nagio|spatial|original|sync|archived|refinery|science|mapping|gitlab|play|ext_|ext-|ext\\.|session|impact|set_|set-|set\\.|see_|see-|see\\.|migration|commit|community|shopify|what\'|cucumber|statamic|mysql|location|tower|line|code|amqp|hello|send|index|high|notebook|alloy|python|field|document|soap|edition|email|php_|php-|php\\.|command|transport|official|upload|study|secure|angularj|akka|scalable|package|request|con_|con-|con\\.|flexible|security|comment|module|flask|graph|flash|apache|change|window|space|lambda|sheet|bookmark|carousel|friend|objective|jekyll|bootstrap|first|article|gwt_|gwt-|gwt\\.|classic|media|websocket|touch|desktop|real|read|recorder|moved|storage|validator|add-on|pusher|scs_|scs-|scs\\.|inline|asp_|asp-|asp\\.|timeline|base|encoding|ffmpeg|kindle|tinymce|pretty|jpa_|jpa-|jpa\\.|used|user|required|webhook|download|resque|espresso|cloud|mongo|benchmark|pure|cakephp|modx|mode|reactive|fuel|written|flickr|mail|brunch|meteor|dynamic|neo_|neo-|neo\\.|new_|new-|new\\.|net_|net-|net\\.|typo|type|keyboard|erlang|adobe|logging|ckeditor|message|iso_|iso-|iso\\.|hook|ldap|folder|reference|railscast|www_|www-|www\\.|tracker|azure|fork|form|digital|exporter|skin|string|template|designer|gollum|fluent|entity|language|alfred|summary|wiki|kernel|calendar|plupload|symfony|foundry|remote|talk|search|dev_|dev-|dev\\.|del_|del-|del\\.|token|idea|sencha|selector|interface|create|fun_|fun-|fun\\.|groovy|query|grail|red_|red-|red\\.|laravel|monkey|slack|supported|instant|value|center|latest|work|but_|but-|but\\.|bug_|bug-|bug\\.|virtual|tweet|statsd|studio|path|real-time|frontend|notifier|coding|tool|firmware|flow|random|mediawiki|bosh|been|beer|lightbox|theory|origin|redmine|hub_|hub-|hub\\.|require|pro_|pro-|pro\\.|ant_|ant-|ant\\.|any_|any-|any\\.|recipe|closure|mapper|event|todo|model|redi|provider|rvm_|rvm-|rvm\\.|program|memcached|rail|silex|foreman|activity|license|strategy|batch|streaming|fast|use_|use-|use\\.|usb_|usb-|usb\\.|impres|academy|slider|please|layer|cros|now_|now-|now\\.|miner|extension|own_|own-|own\\.|app_|app-|app\\.|debian|symphony|example|feature|serie|tree|project|runner|entry|leetcode|layout|webrtc|logic|login|worker|toolkit|mocha|support|back|inside|device|jenkin|contact|fake|awesome|ocaml|bit_|bit-|bit\\.|drive|screen|prototype|gist|binary|nosql|rest|overview|dart|dark|emac|mongoid|solarized|homepage|emulator|commander|django|yandex|gradle|xcode|writer|crm_|crm-|crm\\.|jade|startup|error|using|format|name|spring|parser|scratch|magic|try_|try-|try\\.|rack|directive|challenge|slim|counter|element|chosen|doc_|doc-|doc\\.|meta|should|button|packet|stream|hardware|android|infinite|password|software|ghost|xamarin|spec|chef|interview|hubot|mvc_|mvc-|mvc\\.|exercise|leaflet|launcher|air_|air-|air\\.|photo|board|boxen|way_|way-|way\\.|computing|welcome|notepad|portfolio|cat_|cat-|cat\\.|can_|can-|can\\.|magento|yaml|domain|card|yii_|yii-|yii\\.|checker|browser|upgrade|only|progres|aura|ruby_|ruby-|ruby\\.|polymer|util|lite|hackathon|rule|log_|log-|log\\.|opengl|stanford|skeleton|history|inspector|help|soon|selenium|lab_|lab-|lab\\.|scheme|schema|look|ready|leveldb|docker|game|minimal|logstash|messaging|within|heroku|mongodb|kata|suite|picker|win_|win-|win\\.|wip_|wip-|wip\\.|panel|started|starter|front-end|detector|deploy|editing|based|admin|capture|spree|page|bundle|goal|rpg_|rpg-|rpg\\.|setup|side|mean|reader|cookbook|mini|modern|seed|dom_|dom-|dom\\.|doc_|doc-|doc\\.|dot_|dot-|dot\\.|syntax|sugar|loader|website|make|kit_|kit-|kit\\.|protocol|human|daemon|golang|manager|countdown|connector|swagger|map_|map-|map\\.|mac_|mac-|mac\\.|man_|man-|man\\.|orm_|orm-|orm\\.|org_|org-|org\\.|little|zsh_|zsh-|zsh\\.|shop|show|workshop|money|grid|server|octopres|svn_|svn-|svn\\.|ember|embed|general|file|important|dropbox|portable|public|docpad|fish|sbt_|sbt-|sbt\\.|done|para|network|common|readme|popup|simple|purpose|mirror|single|cordova|exchange|object|design|gateway|account|lamp|intellij|math|mit_|mit-|mit\\.|control|enhanced|emitter|multi|add_|add-|add\\.|about|socket|preview|vagrant|cli_|cli-|cli\\.|powerful|top_|top-|top\\.|radio|watch|fluid|amazon|report|couchbase|automatic|detection|sprite|pyramid|portal|advanced|plu_|plu-|plu\\.|runtime|git_|git-|git\\.|uri_|uri-|uri\\.|haml|node|sql_|sql-|sql\\.|cool|core|obsolete|handler|iphone|extractor|array|copy|nlp_|nlp-|nlp\\.|reveal|pop_|pop-|pop\\.|engine|parse|check|html|nest|all_|all-|all\\.|chinese|buildpack|what|tag_|tag-|tag\\.|proxy|style|cookie|feed|restful|compiler|creating|prelude|context|java|rspec|mock|backbone|light|spotify|flex|related|shell|which|clas|webapp|swift|ansible|unity|console|tumblr|export|campfire|conway\'|made|riak|hero|here|unix|unit|glas|smtp|how_|how-|how\\.|hot_|hot-|hot\\.|debug|release|diff|player|easy|right|old_|old-|old\\.|animate|time|push|explorer|course|training|nette|router|draft|structure|note|salt|where|spark|trello|power|method|social|via_|via-|via\\.|vim_|vim-|vim\\.|select|webkit|github|ftp_|ftp-|ftp\\.|creator|mongoose|led_|led-|led\\.|movie|currently|pdf_|pdf-|pdf\\.|load|markdown|phalcon|input|custom|atom|oracle|phonegap|ubuntu|great|rdf_|rdf-|rdf\\.|popcorn|firefox|zip_|zip-|zip\\.|cuda|dotfile|static|openwrt|viewer|powered|graphic|les_|les-|les\\.|doe_|doe-|doe\\.|maven|word|eclipse|lab_|lab-|lab\\.|hacking|steam|analytic|option|abstract|archive|reality|switcher|club|write|kafka|arduino|angular|online|title|don\'t|contao|notice|analyzer|learning|zend|external|staging|busines|tdd_|tdd-|tdd\\.|scanner|building|snippet|modular|bower|stm_|stm-|stm\\.|lib_|lib-|lib\\.|alpha|mobile|clean|linux|nginx|manifest|some|raspberry|gnome|ide_|ide-|ide\\.|block|statistic|info|drag|youtube|koan|facebook|paperclip|art_|art-|art\\.|quality|tab_|tab-|tab\\.|need|dojo|shield|computer|stat|state|twitter|utility|converter|hosting|devise|liferay|updated|force|tip_|tip-|tip\\.|behavior|active|call|answer|deck|better|principle|ches|bar_|bar-|bar\\.|reddit|three|haxe|just|plug-in|agile|manual|tetri|super|beta|parsing|doctrine|minecraft|useful|perl|sharing|agent|switch|view|dash|channel|repo|pebble|profiler|warning|cluster|running|markup|evented|mod_|mod-|mod\\.|share|csv_|csv-|csv\\.|response|good|house|connect|built|build|find|ipython|webgl|big_|big-|big\\.|google|scala|sdl_|sdl-|sdl\\.|sdk_|sdk-|sdk\\.|native|day_|day-|day\\.|puppet|text|routing|helper|linkedin|crawler|host|guard|merchant|poker|over|writing|free|classe|component|craft|nodej|phoenix|longer|quick|lazy|memory|clone|hacker|middleman|factory|motion|multiple|tornado|hack|ssh_|ssh-|ssh\\.|review|vimrc|driver|driven|blog|particle|table|intro|importer|thrift|xmpp|framework|refresh|react|font|librarie|variou|formatter|analysi|karma|scroll|tut_|tut-|tut\\.|apple|tag_|tag-|tag\\.|tab_|tab-|tab\\.|category|ionic|cache|homebrew|reverse|english|getting|shipping|clojure|boot|book|branch|combination|combo)) 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 (?!(^0x0*|^pub)|.*\\.(bin|json|exe)$|.*(?i)(Client|Factory)$|(^__[A-Za-z]+__$)|^(12345|abcd)|^\\d+(\\.\\d+)?$) 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 entropy (?i)(?:key|api|token|secret|client|passwd|password|auth|access)(?:[0-9a-z\\-_\\t.]{0,20})(?:[\\s|\']|[\\s|"]){0,3}(?:=|>|:=|\\|\\|:|<=|=>|:)(?:\'|@\\"|\\"|\\s|=|\\x60){0,5}(?!([a-z]+\\.[a-zA-Z]+)|.*(\\d{4}-\\d{2}-\\d{2})|:*(?!("|\'))[0-9A-Za-z]+\\.[0-9A-Za-z]+,|[A-Z]+_[A-Z]+_)(?P<CONTENT>[0-9a-z\\-_.=\\~@]{10,150})(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$) (\\d\\.\\d\\.\\d-}|([\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})|(\\w)\\1{5}|(?i)keywords|xxxx|eeeeeeee|0000|\\*\\*\\*|example|test|author=|author("|\')|preview|[A-Z]+_KEY|[.]value|[.]key|-\\d\\.\\d\\.) (\\w|\\.)\\1{5} .*((?i)omitted|arn:aws|(?i)(pub.*key|public.*key)|(?i)clientToken|symbol|cache|author\\.).*', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.generic-api-key') match_id = e568a8303898f65c43738ce9b467b3811ade960877866255d500f56f4cf5486be06aa8df9e775fee32241e3dbf0b475534fcd8256ff0310b842e905efeecb8ee_0
2025-06-06 23:13:33,588 - semgrep.rule_match - DEBUG - match_key = ('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 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 (?!(?i).*(client|endpoint|vpn|_ec2_|aws_|authorize|author|define|config|credential|setting|sample|xxxxxx|000000|buffer|delete|aaaaaa|fewfwef|getenv|env_|system|example|ecdsa|sha256|sha1|sha2|md5|alert|wizard|target|onboard|welcome|page|exploit|experiment|expire|rabbitmq|scraper|widget|music|dns_|dns-|yahoo|want|json|action|script|fix_|fix-|develop|compas|stripe|service|master|metric|tech|gitignore|rich|open|stack|irc_|irc-|sublime|kohana|has_|has-|fabric|wordpres|role|osx_|osx-|boost|addres|queue|working|sandbox|internet|print|vision|tracking|being|generator|traffic|world|pull|rust|watcher|small|auth|full|hash|more|install|auto|complete|learn|paper|installer|research|acces|last|binding|spine|into|chat|algorithm|resource|uploader|video|maker|next|proc|lock|robot|snake|patch|matrix|drill|terminal|term|stuff|genetic|generic|identity|audit|pattern|audio|web_|web-|crud|problem|statu|cms-|cms_|arch|coffee|workflow|changelog|another|uiview|content|kitchen|gnu_|gnu-|gnu\\.|conf|couchdb|client|opencv|rendering|update|concept|varnish|gui_|gui-|gui\\.|version|shared|extra|product|still|not_|not-|not\\.|drop|ring|png_|png-|png\\.|actively|import|output|backup|start|embedded|registry|pool|semantic|instagram|bash|system|ninja|drupal|jquery|polyfill|physic|league|guide|pack|synopsi|sketch|injection|svg_|svg-|svg\\.|friendly|wave|convert|manage|camera|link|slide|timer|wrapper|gallery|url_|url-|url\\.|todomvc|requirej|party|http|payment|async|library|home|coco|gaia|display|universal|func|metadata|hipchat|under|room|config|personal|realtime|resume|database|testing|tiny|basic|forum|meetup|yet_|yet-|yet\\.|cento|dead|fluentd|editor|utilitie|run_|run-|run\\.|box_|box-|box\\.|bot_|bot-|bot\\.|making|sample|group|monitor|ajax|parallel|cassandra|ultimate|site|get_|get-|get\\.|gen_|gen-|gen\\.|gem_|gem-|gem\\.|extended|image|knife|asset|nested|zero|plugin|bracket|mule|mozilla|number|act_|act-|act\\.|map_|map-|map\\.|micro|debug|openshift|chart|expres|backend|task|source|translate|jbos|composer|sqlite|profile|mustache|mqtt|yeoman|have|builder|smart|like|oauth|school|guideline|captcha|filter|bitcoin|bridge|color|toolbox|discovery|new_|new-|new\\.|dashboard|when|setting|level|post|standard|port|platform|yui_|yui-|yui\\.|grunt|animation|haskell|icon|latex|cheat|lua_|lua-|lua\\.|gulp|case|author|without|simulator|wifi|directory|lisp|list|flat|adventure|story|storm|gpu_|gpu-|gpu\\.|store|caching|attention|solr|logger|demo|shortener|hadoop|finder|phone|pipeline|range|textmate|showcase|app_|app-|app\\.|idiomatic|edit|our_|our-|our\\.|out_|out-|out\\.|sentiment|linked|why_|why-|why\\.|local|cube|gmail|job_|job-|job\\.|rpc_|rpc-|rpc\\.|contest|tcp_|tcp-|tcp\\.|usage|buildout|weather|transfer|automated|sphinx|issue|sas_|sas-|sas\\.|parallax|jasmine|addon|machine|solution|dsl_|dsl-|dsl\\.|episode|menu|theme|best|adapter|debugger|chrome|tutorial|life|step|people|joomla|paypal|developer|solver|team|current|love|visual|date|data|canva|container|future|xml_|xml-|xml\\.|twig|nagio|spatial|original|sync|archived|refinery|science|mapping|gitlab|play|ext_|ext-|ext\\.|session|impact|set_|set-|set\\.|see_|see-|see\\.|migration|commit|community|shopify|what\'|cucumber|statamic|mysql|location|tower|line|code|amqp|hello|send|index|high|notebook|alloy|python|field|document|soap|edition|email|php_|php-|php\\.|command|transport|official|upload|study|secure|angularj|akka|scalable|package|request|con_|con-|con\\.|flexible|security|comment|module|flask|graph|flash|apache|change|window|space|lambda|sheet|bookmark|carousel|friend|objective|jekyll|bootstrap|first|article|gwt_|gwt-|gwt\\.|classic|media|websocket|touch|desktop|real|read|recorder|moved|storage|validator|add-on|pusher|scs_|scs-|scs\\.|inline|asp_|asp-|asp\\.|timeline|base|encoding|ffmpeg|kindle|tinymce|pretty|jpa_|jpa-|jpa\\.|used|user|required|webhook|download|resque|espresso|cloud|mongo|benchmark|pure|cakephp|modx|mode|reactive|fuel|written|flickr|mail|brunch|meteor|dynamic|neo_|neo-|neo\\.|new_|new-|new\\.|net_|net-|net\\.|typo|type|keyboard|erlang|adobe|logging|ckeditor|message|iso_|iso-|iso\\.|hook|ldap|folder|reference|railscast|www_|www-|www\\.|tracker|azure|fork|form|digital|exporter|skin|string|template|designer|gollum|fluent|entity|language|alfred|summary|wiki|kernel|calendar|plupload|symfony|foundry|remote|talk|search|dev_|dev-|dev\\.|del_|del-|del\\.|token|idea|sencha|selector|interface|create|fun_|fun-|fun\\.|groovy|query|grail|red_|red-|red\\.|laravel|monkey|slack|supported|instant|value|center|latest|work|but_|but-|but\\.|bug_|bug-|bug\\.|virtual|tweet|statsd|studio|path|real-time|frontend|notifier|coding|tool|firmware|flow|random|mediawiki|bosh|been|beer|lightbox|theory|origin|redmine|hub_|hub-|hub\\.|require|pro_|pro-|pro\\.|ant_|ant-|ant\\.|any_|any-|any\\.|recipe|closure|mapper|event|todo|model|redi|provider|rvm_|rvm-|rvm\\.|program|memcached|rail|silex|foreman|activity|license|strategy|batch|streaming|fast|use_|use-|use\\.|usb_|usb-|usb\\.|impres|academy|slider|please|layer|cros|now_|now-|now\\.|miner|extension|own_|own-|own\\.|app_|app-|app\\.|debian|symphony|example|feature|serie|tree|project|runner|entry|leetcode|layout|webrtc|logic|login|worker|toolkit|mocha|support|back|inside|device|jenkin|contact|fake|awesome|ocaml|bit_|bit-|bit\\.|drive|screen|prototype|gist|binary|nosql|rest|overview|dart|dark|emac|mongoid|solarized|homepage|emulator|commander|django|yandex|gradle|xcode|writer|crm_|crm-|crm\\.|jade|startup|error|using|format|name|spring|parser|scratch|magic|try_|try-|try\\.|rack|directive|challenge|slim|counter|element|chosen|doc_|doc-|doc\\.|meta|should|button|packet|stream|hardware|android|infinite|password|software|ghost|xamarin|spec|chef|interview|hubot|mvc_|mvc-|mvc\\.|exercise|leaflet|launcher|air_|air-|air\\.|photo|board|boxen|way_|way-|way\\.|computing|welcome|notepad|portfolio|cat_|cat-|cat\\.|can_|can-|can\\.|magento|yaml|domain|card|yii_|yii-|yii\\.|checker|browser|upgrade|only|progres|aura|ruby_|ruby-|ruby\\.|polymer|util|lite|hackathon|rule|log_|log-|log\\.|opengl|stanford|skeleton|history|inspector|help|soon|selenium|lab_|lab-|lab\\.|scheme|schema|look|ready|leveldb|docker|game|minimal|logstash|messaging|within|heroku|mongodb|kata|suite|picker|win_|win-|win\\.|wip_|wip-|wip\\.|panel|started|starter|front-end|detector|deploy|editing|based|admin|capture|spree|page|bundle|goal|rpg_|rpg-|rpg\\.|setup|side|mean|reader|cookbook|mini|modern|seed|dom_|dom-|dom\\.|doc_|doc-|doc\\.|dot_|dot-|dot\\.|syntax|sugar|loader|website|make|kit_|kit-|kit\\.|protocol|human|daemon|golang|manager|countdown|connector|swagger|map_|map-|map\\.|mac_|mac-|mac\\.|man_|man-|man\\.|orm_|orm-|orm\\.|org_|org-|org\\.|little|zsh_|zsh-|zsh\\.|shop|show|workshop|money|grid|server|octopres|svn_|svn-|svn\\.|ember|embed|general|file|important|dropbox|portable|public|docpad|fish|sbt_|sbt-|sbt\\.|done|para|network|common|readme|popup|simple|purpose|mirror|single|cordova|exchange|object|design|gateway|account|lamp|intellij|math|mit_|mit-|mit\\.|control|enhanced|emitter|multi|add_|add-|add\\.|about|socket|preview|vagrant|cli_|cli-|cli\\.|powerful|top_|top-|top\\.|radio|watch|fluid|amazon|report|couchbase|automatic|detection|sprite|pyramid|portal|advanced|plu_|plu-|plu\\.|runtime|git_|git-|git\\.|uri_|uri-|uri\\.|haml|node|sql_|sql-|sql\\.|cool|core|obsolete|handler|iphone|extractor|array|copy|nlp_|nlp-|nlp\\.|reveal|pop_|pop-|pop\\.|engine|parse|check|html|nest|all_|all-|all\\.|chinese|buildpack|what|tag_|tag-|tag\\.|proxy|style|cookie|feed|restful|compiler|creating|prelude|context|java|rspec|mock|backbone|light|spotify|flex|related|shell|which|clas|webapp|swift|ansible|unity|console|tumblr|export|campfire|conway\'|made|riak|hero|here|unix|unit|glas|smtp|how_|how-|how\\.|hot_|hot-|hot\\.|debug|release|diff|player|easy|right|old_|old-|old\\.|animate|time|push|explorer|course|training|nette|router|draft|structure|note|salt|where|spark|trello|power|method|social|via_|via-|via\\.|vim_|vim-|vim\\.|select|webkit|github|ftp_|ftp-|ftp\\.|creator|mongoose|led_|led-|led\\.|movie|currently|pdf_|pdf-|pdf\\.|load|markdown|phalcon|input|custom|atom|oracle|phonegap|ubuntu|great|rdf_|rdf-|rdf\\.|popcorn|firefox|zip_|zip-|zip\\.|cuda|dotfile|static|openwrt|viewer|powered|graphic|les_|les-|les\\.|doe_|doe-|doe\\.|maven|word|eclipse|lab_|lab-|lab\\.|hacking|steam|analytic|option|abstract|archive|reality|switcher|club|write|kafka|arduino|angular|online|title|don\'t|contao|notice|analyzer|learning|zend|external|staging|busines|tdd_|tdd-|tdd\\.|scanner|building|snippet|modular|bower|stm_|stm-|stm\\.|lib_|lib-|lib\\.|alpha|mobile|clean|linux|nginx|manifest|some|raspberry|gnome|ide_|ide-|ide\\.|block|statistic|info|drag|youtube|koan|facebook|paperclip|art_|art-|art\\.|quality|tab_|tab-|tab\\.|need|dojo|shield|computer|stat|state|twitter|utility|converter|hosting|devise|liferay|updated|force|tip_|tip-|tip\\.|behavior|active|call|answer|deck|better|principle|ches|bar_|bar-|bar\\.|reddit|three|haxe|just|plug-in|agile|manual|tetri|super|beta|parsing|doctrine|minecraft|useful|perl|sharing|agent|switch|view|dash|channel|repo|pebble|profiler|warning|cluster|running|markup|evented|mod_|mod-|mod\\.|share|csv_|csv-|csv\\.|response|good|house|connect|built|build|find|ipython|webgl|big_|big-|big\\.|google|scala|sdl_|sdl-|sdl\\.|sdk_|sdk-|sdk\\.|native|day_|day-|day\\.|puppet|text|routing|helper|linkedin|crawler|host|guard|merchant|poker|over|writing|free|classe|component|craft|nodej|phoenix|longer|quick|lazy|memory|clone|hacker|middleman|factory|motion|multiple|tornado|hack|ssh_|ssh-|ssh\\.|review|vimrc|driver|driven|blog|particle|table|intro|importer|thrift|xmpp|framework|refresh|react|font|librarie|variou|formatter|analysi|karma|scroll|tut_|tut-|tut\\.|apple|tag_|tag-|tag\\.|tab_|tab-|tab\\.|category|ionic|cache|homebrew|reverse|english|getting|shipping|clojure|boot|book|branch|combination|combo)) 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 (?!(^0x0*|^pub)|.*\\.(bin|json|exe)$|.*(?i)(Client|Factory)$|(^__[A-Za-z]+__$)|^(12345|abcd)|^\\d+(\\.\\d+)?$) 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 entropy (?i)(?:key|api|token|secret|client|passwd|password|auth|access)(?:[0-9a-z\\-_\\t.]{0,20})(?:[\\s|\']|[\\s|"]){0,3}(?:=|>|:=|\\|\\|:|<=|=>|:)(?:\'|@\\"|\\"|\\s|=|\\x60){0,5}(?!([a-z]+\\.[a-zA-Z]+)|.*(\\d{4}-\\d{2}-\\d{2})|:*(?!("|\'))[0-9A-Za-z]+\\.[0-9A-Za-z]+,|[A-Z]+_[A-Z]+_)(?P<CONTENT>[0-9a-z\\-_.=\\~@]{10,150})(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$) (\\d\\.\\d\\.\\d-}|([\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})|(\\w)\\1{5}|(?i)keywords|xxxx|eeeeeeee|0000|\\*\\*\\*|example|test|author=|author("|\')|preview|[A-Z]+_KEY|[.]value|[.]key|-\\d\\.\\d\\.) (\\w|\\.)\\1{5} .*((?i)omitted|arn:aws|(?i)(pub.*key|public.*key)|(?i)clientToken|symbol|cache|author\\.).*', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.generic-api-key') match_id = e568a8303898f65c43738ce9b467b3811ade960877866255d500f56f4cf5486be06aa8df9e775fee32241e3dbf0b475534fcd8256ff0310b842e905efeecb8ee_0
2025-06-06 23:13:33,589 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = bad1f9a65aa6ffc34be3c9b4eba6907d800b32c637f3d71e87e49d7cffdabc3ed7ab0f0a157e893d0a52f84d44756da0d75d5fc2772bb32547c4b6cef2ada4cd_0
2025-06-06 23:13:33,590 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 3e50d6ea8a500614622e79a105f3dfbaa997e770c8cb4a1e768cc27a408d498a88b2f636af438b2512d373fe2881663b8e3f9c7be997c47d38b85d430493174e_0
2025-06-06 23:13:33,591 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 3e50d6ea8a500614622e79a105f3dfbaa997e770c8cb4a1e768cc27a408d498a88b2f636af438b2512d373fe2881663b8e3f9c7be997c47d38b85d430493174e_0
2025-06-06 23:13:33,592 - semgrep.rule_match - DEBUG - match_key = ('(?i)\\b(sk-[a-zA-Z0-9_-]+)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.openai-api-key') match_id = 3e50d6ea8a500614622e79a105f3dfbaa997e770c8cb4a1e768cc27a408d498a88b2f636af438b2512d373fe2881663b8e3f9c7be997c47d38b85d430493174e_0
2025-06-06 23:13:33,593 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-06 23:13:33,594 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,596 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,597 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,598 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-06 23:13:33,599 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,600 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,602 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,603 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-06 23:13:33,604 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,606 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,607 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_1
2025-06-06 23:13:33,608 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-06 23:13:33,609 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,610 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,611 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_1
2025-06-06 23:13:33,612 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-06 23:13:33,613 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,614 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,616 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_2
2025-06-06 23:13:33,617 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-06 23:13:33,618 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,620 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,621 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_2
2025-06-06 23:13:33,622 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-06 23:13:33,623 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,624 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,625 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_3
2025-06-06 23:13:33,626 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-06 23:13:33,627 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,628 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,630 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_3
2025-06-06 23:13:33,631 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-06 23:13:33,632 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,633 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,635 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_4
2025-06-06 23:13:33,636 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-06 23:13:33,637 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,639 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,640 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_4
2025-06-06 23:13:33,641 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-06 23:13:33,643 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,644 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,645 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_5
2025-06-06 23:13:33,646 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-06 23:13:33,647 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,649 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,650 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_5
2025-06-06 23:13:33,651 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-06 23:13:33,652 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,654 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,655 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_6
2025-06-06 23:13:33,656 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-06 23:13:33,657 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,658 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,659 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_6
2025-06-06 23:13:33,660 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = 24362f3d01aa82fbc7e205a04ef7dd4e7bf634321302920215f4723e4ac64b5f91747fc0b6668c297920980ee4bf897f392cd6f9d20e4c54005cab07da5c8e9b_0
2025-06-06 23:13:33,661 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,662 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_0
2025-06-06 23:13:33,663 - semgrep.rule_match - DEBUG - match_key = ('eyJ[A-Za-z0-9-_=]{14,}\\.[A-Za-z0-9-_=]{13,}\\.?[A-Za-z0-9-_.+/=]*?', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.security.detected-jwt-token') match_id = a2319f19ec98670a2840de936f40903630ba491ff5201b59c759d30969c58ef8a6bf7a49b2ec6c9d1d37698bd9bca9b5acf97b4167c4b2e49aee0fc81fb5f10e_7
2025-06-06 23:13:33,665 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = d7f9da2d3151c0cf12d72ff01c8e19d71ca908d6e1a2c973f55c8a8a329498e0f866c8186985836cadc293d7ef4becd78d2252a0e2d638d73e36e8fab0a56715_0
2025-06-06 23:13:33,666 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,667 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_0
2025-06-06 23:13:33,668 - semgrep.rule_match - DEBUG - match_key = ('\\b(ey[a-zA-Z0-9]{17,}\\.ey[a-zA-Z0-9\\/\\\\_-]{17,}\\.(?:[a-zA-Z0-9\\/\\\\_-]{10,}={0,2})?)(?:[\'|\\"|\\n|\\r|\\s|\\x60|;]|$)', PosixPath('attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt'), 'config..semgrep.vendored-rules.generic.secrets.gitleaks.jwt') match_id = 51df4795e3892b5429d087b6ee6ffed788b0607827ec759e148f0591da43c98253a8bc2e6abe0c9c232d4273ca6359337267adf88e654f909b9f4607ee8f6205_7
2025-06-06 23:13:33,669 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0ee74fd49637bebe183eca7188dbde26e386314e62cc2e7ba1ee60b377b638243fcd84e6c6fa04886198ccacfa6a711bfbcc61a28f9ddc913d5b3c53083cbc90_0
2025-06-06 23:13:33,669 - semgrep.rule_match - DEBUG - match_key = ('\n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" > <script \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0c462f34c2e5c0115c98099a09d5fde04ba5aac86f96eda89dc79e1650b67f17507e9f0d764590150610e5e58e4f87c72431ae06d0266dd4ca65d989374ab76e_0
2025-06-06 23:13:33,670 - semgrep.rule_match - DEBUG - match_key = ('\n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" > <script \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0c462f34c2e5c0115c98099a09d5fde04ba5aac86f96eda89dc79e1650b67f17507e9f0d764590150610e5e58e4f87c72431ae06d0266dd4ca65d989374ab76e_0
2025-06-06 23:13:33,670 - semgrep.rule_match - DEBUG - match_key = ('\n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" > <script \n      type="text/javascript"\n      src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0c462f34c2e5c0115c98099a09d5fde04ba5aac86f96eda89dc79e1650b67f17507e9f0d764590150610e5e58e4f87c72431ae06d0266dd4ca65d989374ab76e_0
2025-06-06 23:13:33,671 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.possible-nginx-h2c-smuggling') match_id = 660e4ea421c5e211d693000daff047303b04d9b1dc85a0a478c9d6f41550386d7a0a5f9668da89e45a82923879ec95fb56615405245166dd66e1bff5db071fba_0
2025-06-06 23:13:33,671 - semgrep.rule_match - DEBUG - match_key = ('location ... {\n  ...\n}\n proxy_http_version 1.1 ...;\n...\nproxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_http_version 1.1 ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n...\nproxy_http_version 1.1 ...;\n', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.possible-nginx-h2c-smuggling') match_id = ae6907400de063e188ef4540fa76d34ea58319f2c3b5774a76ec4dda7dfde9c9f12e9f91507a7d9cbd5a57d2a3afc7559f097362f1431347b0cbde5079ab1a53_0
2025-06-06 23:13:33,672 - semgrep.rule_match - DEBUG - match_key = ('location ... {\n  ...\n}\n proxy_http_version 1.1 ...;\n...\nproxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_http_version 1.1 ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n...\nproxy_http_version 1.1 ...;\n', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.possible-nginx-h2c-smuggling') match_id = ae6907400de063e188ef4540fa76d34ea58319f2c3b5774a76ec4dda7dfde9c9f12e9f91507a7d9cbd5a57d2a3afc7559f097362f1431347b0cbde5079ab1a53_0
2025-06-06 23:13:33,672 - semgrep.rule_match - DEBUG - match_key = ('location ... {\n  ...\n}\n proxy_http_version 1.1 ...;\n...\nproxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_http_version 1.1 ...;\n...\nproxy_set_header Connection ...;\n proxy_set_header Upgrade ...;\n...\nproxy_set_header Connection ...;\n...\nproxy_http_version 1.1 ...;\n', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.possible-nginx-h2c-smuggling') match_id = ae6907400de063e188ef4540fa76d34ea58319f2c3b5774a76ec4dda7dfde9c9f12e9f91507a7d9cbd5a57d2a3afc7559f097362f1431347b0cbde5079ab1a53_0
2025-06-06 23:13:33,673 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.request-host-used') match_id = 327a18752ef8dd885cb20efad7118994364aa67a9696b0144c9d9d3b222b9dc96375e3d614939649799ce0b488347523b54919c9d313c25997ffc4672be41eaa_0
2025-06-06 23:13:33,673 - semgrep.rule_match - DEBUG - match_key = ('$host $http_host', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.request-host-used') match_id = 5ac33422430ce88039eda001e6e2112be87ee0b4840d6dee322e36fd258af6aff9d5f10970384d1948b4b80ff92a7df8ac5ce6067eac11b7dbcb557f5bc5ccc9_0
2025-06-06 23:13:33,674 - semgrep.rule_match - DEBUG - match_key = ('$host $http_host', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.request-host-used') match_id = 5ac33422430ce88039eda001e6e2112be87ee0b4840d6dee322e36fd258af6aff9d5f10970384d1948b4b80ff92a7df8ac5ce6067eac11b7dbcb557f5bc5ccc9_0
2025-06-06 23:13:33,674 - semgrep.rule_match - DEBUG - match_key = ('$host $http_host', PosixPath('client/nginx.conf'), 'config..semgrep.vendored-rules.generic.nginx.security.request-host-used') match_id = 5ac33422430ce88039eda001e6e2112be87ee0b4840d6dee322e36fd258af6aff9d5f10970384d1948b4b80ff92a7df8ac5ce6067eac11b7dbcb557f5bc5ccc9_0
2025-06-06 23:13:33,675 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/components/flashcards/AiFlashcardGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 48b9a704cbda6c074e8b6c11305b58cc36af2e309003a24ba9e25b89f56d6b9be93e8d88150abefc64ad5f26214029681c8abc74bf654da56bdd0bd4ab0406cb_0
2025-06-06 23:13:33,676 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/components/flashcards/AiFlashcardGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = bd1fac6af7e75e1d2b23d59c800579bdf1fb88589236269386cb11c2e882820a2cb0e9cec992aeeefff947284a85cb832b2cd32eca3cab650662a411e3c383d1_0
2025-06-06 23:13:33,677 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/components/flashcards/AiFlashcardGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = bd1fac6af7e75e1d2b23d59c800579bdf1fb88589236269386cb11c2e882820a2cb0e9cec992aeeefff947284a85cb832b2cd32eca3cab650662a411e3c383d1_0
2025-06-06 23:13:33,678 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/components/flashcards/AiFlashcardGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = bd1fac6af7e75e1d2b23d59c800579bdf1fb88589236269386cb11c2e882820a2cb0e9cec992aeeefff947284a85cb832b2cd32eca3cab650662a411e3c383d1_0
2025-06-06 23:13:33,678 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/components/quiz/AiQuestionGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e4ab50d8f5c45b8520c6f9625b520b8577b79a88fdf0b28ec4fd75e6d460029dc55393b6555ff65507139fd4df1ca6194fa847f7b10ddfb364f0edd6c0efff38_0
2025-06-06 23:13:33,679 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Added questionsToAdd.length questions to quiz selectedQuizId. Response: $UTIL = require(\'util\')\n...\n $UTIL.format(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n console.log(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n', PosixPath('client/src/components/quiz/AiQuestionGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = aa3c9374ac47fd0d72505d61e23d918c36c3a7f19cf5f31a42e54f2cc924153df8191e5bf4dd4fa3de008288263b1645bd0365046c96646cb737a3917f1cb34c_0
2025-06-06 23:13:33,680 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Added questionsToAdd.length questions to quiz selectedQuizId. Response: $UTIL = require(\'util\')\n...\n $UTIL.format(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n console.log(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n', PosixPath('client/src/components/quiz/AiQuestionGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = aa3c9374ac47fd0d72505d61e23d918c36c3a7f19cf5f31a42e54f2cc924153df8191e5bf4dd4fa3de008288263b1645bd0365046c96646cb737a3917f1cb34c_0
2025-06-06 23:13:33,681 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Added questionsToAdd.length questions to quiz selectedQuizId. Response: $UTIL = require(\'util\')\n...\n $UTIL.format(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n console.log(`Added questionsToAdd.length questions to quiz selectedQuizId. Response:,addedQuestions,...)\n', PosixPath('client/src/components/quiz/AiQuestionGenerator.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = aa3c9374ac47fd0d72505d61e23d918c36c3a7f19cf5f31a42e54f2cc924153df8191e5bf4dd4fa3de008288263b1645bd0365046c96646cb737a3917f1cb34c_0
2025-06-06 23:13:33,681 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/lib/ai-provider.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 92d33de102e78a0060215232fcc57e4308c353de0445afd28995e3c89f87eea63e907a86cd6c47f45673f257c35c1e1a74797e4b1d60353a41b1b52fb3df3c38_0
2025-06-06 23:13:33,682 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `✅ isAIProviderConfigured: Result for settings.provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`✅ isAIProviderConfigured: Result for settings.provider:,{success credentialsResult.success hasCredentials credentialsResult.hasCredentials isConfigured isConfigured},...)\n console.log(`✅ isAIProviderConfigured: Result for settings.provider:,{success credentialsResult.success hasCredentials credentialsResult.hasCredentials isConfigured isConfigured},...)\n', PosixPath('client/src/lib/ai-provider.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f7414f3d91c10539d2ce07a6f00f2e3796f4108736c02fdd7c3a725194c4775318032ce7885d041bf10bdf7ed056758d68b8ff5ea5f47190d4263a17cec35fd6_0
2025-06-06 23:13:33,683 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `✅ isAIProviderConfigured: Result for settings.provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`✅ isAIProviderConfigured: Result for settings.provider:,{success credentialsResult.success hasCredentials credentialsResult.hasCredentials isConfigured isConfigured},...)\n console.log(`✅ isAIProviderConfigured: Result for settings.provider:,{success credentialsResult.success hasCredentials credentialsResult.hasCredentials isConfigured isConfigured},...)\n', PosixPath('client/src/lib/ai-provider.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f7414f3d91c10539d2ce07a6f00f2e3796f4108736c02fdd7c3a725194c4775318032ce7885d041bf10bdf7ed056758d68b8ff5ea5f47190d4263a17cec35fd6_0
2025-06-06 23:13:33,684 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `✅ isAIProviderConfigured: Result for settings.provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`✅ isAIProviderConfigured: Result for settings.provider:,{success credentialsResult.success hasCredentials credentialsResult.hasCredentials isConfigured isConfigured},...)\n console.log(`✅ isAIProviderConfigured: Result for settings.provider:,{success credentialsResult.success hasCredentials credentialsResult.hasCredentials isConfigured isConfigured},...)\n', PosixPath('client/src/lib/ai-provider.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f7414f3d91c10539d2ce07a6f00f2e3796f4108736c02fdd7c3a725194c4775318032ce7885d041bf10bdf7ed056758d68b8ff5ea5f47190d4263a17cec35fd6_0
2025-06-06 23:13:33,685 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/lib/api.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = da5d03aaa0a3e90d69d826b3ae4dd0d3a7e2653fda84a045333473d08d9a234629800afae8c84ca1752f4ac18e5aacf17bcfeb3eac0f06950c97a6331e7f8be8_0
2025-06-06 23:13:33,686 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `✅ getCredentialsAPI: Successfully retrieved credentials for provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`✅ getCredentialsAPI: Successfully retrieved credentials for provider:,{success result.success hasCredentials result.hasCredentials},...)\n console.log(`✅ getCredentialsAPI: Successfully retrieved credentials for provider:,{success result.success hasCredentials result.hasCredentials},...)\n', PosixPath('client/src/lib/api.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = d729bdf7d95f0c8b2c75f096003455d8ad54a1b82359c663122d605649e640cade0f6a64120810816fe177165dce14a6ab1ba0abc828a9f85761536ff54dfed9_0
2025-06-06 23:13:33,688 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `✅ getCredentialsAPI: Successfully retrieved credentials for provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`✅ getCredentialsAPI: Successfully retrieved credentials for provider:,{success result.success hasCredentials result.hasCredentials},...)\n console.log(`✅ getCredentialsAPI: Successfully retrieved credentials for provider:,{success result.success hasCredentials result.hasCredentials},...)\n', PosixPath('client/src/lib/api.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = d729bdf7d95f0c8b2c75f096003455d8ad54a1b82359c663122d605649e640cade0f6a64120810816fe177165dce14a6ab1ba0abc828a9f85761536ff54dfed9_0
2025-06-06 23:13:33,689 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `✅ getCredentialsAPI: Successfully retrieved credentials for provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`✅ getCredentialsAPI: Successfully retrieved credentials for provider:,{success result.success hasCredentials result.hasCredentials},...)\n console.log(`✅ getCredentialsAPI: Successfully retrieved credentials for provider:,{success result.success hasCredentials result.hasCredentials},...)\n', PosixPath('client/src/lib/api.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = d729bdf7d95f0c8b2c75f096003455d8ad54a1b82359c663122d605649e640cade0f6a64120810816fe177165dce14a6ab1ba0abc828a9f85761536ff54dfed9_0
2025-06-06 23:13:33,690 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/pages/FlashcardsPage.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = b207cb55c60a0222cf29d53226a7984cc7f97441a09bbc164a4b2bf4d1affd05d64d605a6abf799846d69de9e7aa4f022d26a501823580e64d773d28a692100b_0
2025-06-06 23:13:33,691 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/pages/FlashcardsPage.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f3bfd07b56fa42130ffd546148e5c7a0d8bc75a38afc29cf4f0e3401196fcd6e352f77bf69fb792a956da31a76f367e56b53bc191bd269133280ba4a83f2f268_0
2025-06-06 23:13:33,692 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/pages/FlashcardsPage.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f3bfd07b56fa42130ffd546148e5c7a0d8bc75a38afc29cf4f0e3401196fcd6e352f77bf69fb792a956da31a76f367e56b53bc191bd269133280ba4a83f2f268_0
2025-06-06 23:13:33,693 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error fetching document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error fetching document docId:,err,...)\n console.error(`Error fetching document docId:,err,...)\n', PosixPath('client/src/pages/FlashcardsPage.tsx'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f3bfd07b56fa42130ffd546148e5c7a0d8bc75a38afc29cf4f0e3401196fcd6e352f77bf69fb792a956da31a76f367e56b53bc191bd269133280ba4a83f2f268_0
2025-06-06 23:13:33,694 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/index.ts'), 'config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write') match_id = f96200e70d96e050ff0d48a206612e913a78ae8d9482c012ee690fafcbb91b6cf36929fc1b7be05f92e44386866d8f5610990d984b647449adbeaaf1cabddf9c_0
2025-06-06 23:13:33,695 - semgrep.rule_match - DEBUG - match_key = ("$APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.set('$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {...}) $METHOD ^(get|post|put|head|delete|options) function ... (req, res) {...} function ... (req, res, next) {...} req.body req.params req.query function ... (req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res) {\n    ...\n    res.set('$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.set('$TYPE')\n}\n req ({ req }: Request,res: Response) => {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response) => {...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.set('$TYPE')\n}\n body params query value res. ... .set('...'). ... .send(value) res. ... .type('...'). ... .send(value) res.$METHOD({ ... }) res.send(value) res.write(value) function ... (..., res,...) {...}", PosixPath('server/index.ts'), 'config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write') match_id = f36b6aa5df41601445443e25ee24c78dc3b1ab71697614b46b6f62d5e2686326d9ed73a5abd635b3703de01a746e913adc48c823e3572792b885c9da14f29098_0
2025-06-06 23:13:33,696 - semgrep.rule_match - DEBUG - match_key = ("$APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.set('$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {...}) $METHOD ^(get|post|put|head|delete|options) function ... (req, res) {...} function ... (req, res, next) {...} req.body req.params req.query function ... (req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res) {\n    ...\n    res.set('$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.set('$TYPE')\n}\n req ({ req }: Request,res: Response) => {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response) => {...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.set('$TYPE')\n}\n body params query value res. ... .set('...'). ... .send(value) res. ... .type('...'). ... .send(value) res.$METHOD({ ... }) res.send(value) res.write(value) function ... (..., res,...) {...}", PosixPath('server/index.ts'), 'config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write') match_id = f36b6aa5df41601445443e25ee24c78dc3b1ab71697614b46b6f62d5e2686326d9ed73a5abd635b3703de01a746e913adc48c823e3572792b885c9da14f29098_0
2025-06-06 23:13:33,696 - semgrep.rule_match - DEBUG - match_key = ("$APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {\n    ...\n    res.set('$TYPE')\n})\n $APP.$METHOD(..., function $FUNC(req, res) {...}) $METHOD ^(get|post|put|head|delete|options) function ... (req, res) {...} function ... (req, res, next) {...} req.body req.params req.query function ... (req, res) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res) {\n    ...\n    res.set('$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n function ... (req, res, next) {\n    ...\n    res.set('$TYPE')\n}\n req ({ req }: Request,res: Response) => {\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response) => {...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{...}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.$SET('Content-Type', '$TYPE')\n}\n ({ req }: Request,res: Response, next: NextFunction) =>\n{\n    ...\n    res.set('$TYPE')\n}\n body params query value res. ... .set('...'). ... .send(value) res. ... .type('...'). ... .send(value) res.$METHOD({ ... }) res.send(value) res.write(value) function ... (..., res,...) {...}", PosixPath('server/index.ts'), 'config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write') match_id = f36b6aa5df41601445443e25ee24c78dc3b1ab71697614b46b6f62d5e2686326d9ed73a5abd635b3703de01a746e913adc48c823e3572792b885c9da14f29098_0
2025-06-06 23:13:33,697 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f64a372d7133973be614e73c449473a6277b68ed87e134dcf5009c0bb07f7c78e753f1ecd79055eb94bddb74ac67e28f69ee8011a86ef9888bcc024371ee9c63_0
2025-06-06 23:13:33,698 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `✅ [Credentials] Successfully decrypted credentials for user userId: $UTIL = require(\'util\')\n...\n $UTIL.format(`✅ [Credentials] Successfully decrypted credentials for user userId:,{hasApiKey!!decryptedApiKey apiKeyLength decryptedApiKey?.?.length baseUrl data.base_url extractionModel data.extraction_model generationModel data.generation_model},...)\n console.log(`✅ [Credentials] Successfully decrypted credentials for user userId:,{hasApiKey!!decryptedApiKey apiKeyLength decryptedApiKey?.?.length baseUrl data.base_url extractionModel data.extraction_model generationModel data.generation_model},...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 8b24346f93c493e32746e14e628723a9956b8a8e17f0c776180d05709c37f59e492d57c2f2cee2e0f385987fc5109c1be7788325b22e52f03705447f379b41a0_0
2025-06-06 23:13:33,698 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `✅ [Credentials] Successfully decrypted credentials for user userId: $UTIL = require(\'util\')\n...\n $UTIL.format(`✅ [Credentials] Successfully decrypted credentials for user userId:,{hasApiKey!!decryptedApiKey apiKeyLength decryptedApiKey?.?.length baseUrl data.base_url extractionModel data.extraction_model generationModel data.generation_model},...)\n console.log(`✅ [Credentials] Successfully decrypted credentials for user userId:,{hasApiKey!!decryptedApiKey apiKeyLength decryptedApiKey?.?.length baseUrl data.base_url extractionModel data.extraction_model generationModel data.generation_model},...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 8b24346f93c493e32746e14e628723a9956b8a8e17f0c776180d05709c37f59e492d57c2f2cee2e0f385987fc5109c1be7788325b22e52f03705447f379b41a0_0
2025-06-06 23:13:33,699 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `✅ [Credentials] Successfully decrypted credentials for user userId: $UTIL = require(\'util\')\n...\n $UTIL.format(`✅ [Credentials] Successfully decrypted credentials for user userId:,{hasApiKey!!decryptedApiKey apiKeyLength decryptedApiKey?.?.length baseUrl data.base_url extractionModel data.extraction_model generationModel data.generation_model},...)\n console.log(`✅ [Credentials] Successfully decrypted credentials for user userId:,{hasApiKey!!decryptedApiKey apiKeyLength decryptedApiKey?.?.length baseUrl data.base_url extractionModel data.extraction_model generationModel data.generation_model},...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 8b24346f93c493e32746e14e628723a9956b8a8e17f0c776180d05709c37f59e492d57c2f2cee2e0f385987fc5109c1be7788325b22e52f03705447f379b41a0_0
2025-06-06 23:13:33,700 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f64a372d7133973be614e73c449473a6277b68ed87e134dcf5009c0bb07f7c78e753f1ecd79055eb94bddb74ac67e28f69ee8011a86ef9888bcc024371ee9c63_0
2025-06-06 23:13:33,701 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `❌ [Credentials] Failed to decrypt API key for user userId: $UTIL = require(\'util\')\n...\n $UTIL.format(`❌ [Credentials] Failed to decrypt API key for user userId:,decryptError,...)\n console.error(`❌ [Credentials] Failed to decrypt API key for user userId:,decryptError,...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ff3834d144b4065636dc5ddf1dfc7704c8db4a512592dc61e6641f251e9fc2240feb6e492fadd4c18a9451c54b4cc95644984393dc13c8bfeb19009f70fece1b_0
2025-06-06 23:13:33,702 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `❌ [Credentials] Failed to decrypt API key for user userId: $UTIL = require(\'util\')\n...\n $UTIL.format(`❌ [Credentials] Failed to decrypt API key for user userId:,decryptError,...)\n console.error(`❌ [Credentials] Failed to decrypt API key for user userId:,decryptError,...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ff3834d144b4065636dc5ddf1dfc7704c8db4a512592dc61e6641f251e9fc2240feb6e492fadd4c18a9451c54b4cc95644984393dc13c8bfeb19009f70fece1b_0
2025-06-06 23:13:33,702 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `❌ [Credentials] Failed to decrypt API key for user userId: $UTIL = require(\'util\')\n...\n $UTIL.format(`❌ [Credentials] Failed to decrypt API key for user userId:,decryptError,...)\n console.error(`❌ [Credentials] Failed to decrypt API key for user userId:,decryptError,...)\n', PosixPath('server/middleware/apiKeyStorage.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ff3834d144b4065636dc5ddf1dfc7704c8db4a512592dc61e6641f251e9fc2240feb6e492fadd4c18a9451c54b4cc95644984393dc13c8bfeb19009f70fece1b_0
2025-06-06 23:13:33,703 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 2cbe758bbcb423f936d4160238e6480dafd27c59ce17de152e81dbf3e97229ea7753437b4b88f669b963a05ba6766219c92e2923287e7a2350bd442c23004ecf_0
2025-06-06 23:13:33,704 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `GET /api/credentials/req.params.provider - Body keys: $UTIL = require(\'util\')\n...\n $UTIL.format(`GET /api/credentials/req.params.provider - Body keys:,Object.keys(req.body),...)\n console.log(`GET /api/credentials/req.params.provider - Body keys:,Object.keys(req.body),...)\n', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 494b0a991970c440f07d76920f3f47e4b8af7b4f68a2040d2d258525d0a99f8048d9e0cd19e32828e67cbcef4dc39688a7d999761d0917d3da5854fb5e51b477_0
2025-06-06 23:13:33,705 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `GET /api/credentials/req.params.provider - Body keys: $UTIL = require(\'util\')\n...\n $UTIL.format(`GET /api/credentials/req.params.provider - Body keys:,Object.keys(req.body),...)\n console.log(`GET /api/credentials/req.params.provider - Body keys:,Object.keys(req.body),...)\n', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 494b0a991970c440f07d76920f3f47e4b8af7b4f68a2040d2d258525d0a99f8048d9e0cd19e32828e67cbcef4dc39688a7d999761d0917d3da5854fb5e51b477_0
2025-06-06 23:13:33,706 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `GET /api/credentials/req.params.provider - Body keys: $UTIL = require(\'util\')\n...\n $UTIL.format(`GET /api/credentials/req.params.provider - Body keys:,Object.keys(req.body),...)\n console.log(`GET /api/credentials/req.params.provider - Body keys:,Object.keys(req.body),...)\n', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 494b0a991970c440f07d76920f3f47e4b8af7b4f68a2040d2d258525d0a99f8048d9e0cd19e32828e67cbcef4dc39688a7d999761d0917d3da5854fb5e51b477_0
2025-06-06 23:13:33,706 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 2cbe758bbcb423f936d4160238e6480dafd27c59ce17de152e81dbf3e97229ea7753437b4b88f669b963a05ba6766219c92e2923287e7a2350bd442c23004ecf_0
2025-06-06 23:13:33,707 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `❌ Failed to retrieve credentials for provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`❌ Failed to retrieve credentials for provider:,result.error,...)\n console.error(`❌ Failed to retrieve credentials for provider:,result.error,...)\n', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 318066618c0fd7cb330806fa8f5ce9d3ca378f04b63eec69307aa90a0accbc9c05c26553ad31f799b3a334f78c664d0fc2d90ab39791a99082714359a98a0d36_0
2025-06-06 23:13:33,708 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `❌ Failed to retrieve credentials for provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`❌ Failed to retrieve credentials for provider:,result.error,...)\n console.error(`❌ Failed to retrieve credentials for provider:,result.error,...)\n', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 318066618c0fd7cb330806fa8f5ce9d3ca378f04b63eec69307aa90a0accbc9c05c26553ad31f799b3a334f78c664d0fc2d90ab39791a99082714359a98a0d36_0
2025-06-06 23:13:33,709 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `❌ Failed to retrieve credentials for provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`❌ Failed to retrieve credentials for provider:,result.error,...)\n console.error(`❌ Failed to retrieve credentials for provider:,result.error,...)\n', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 318066618c0fd7cb330806fa8f5ce9d3ca378f04b63eec69307aa90a0accbc9c05c26553ad31f799b3a334f78c664d0fc2d90ab39791a99082714359a98a0d36_0
2025-06-06 23:13:33,710 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 2cbe758bbcb423f936d4160238e6480dafd27c59ce17de152e81dbf3e97229ea7753437b4b88f669b963a05ba6766219c92e2923287e7a2350bd442c23004ecf_0
2025-06-06 23:13:33,711 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `❌ Error retrieving credentials for req.params.provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`❌ Error retrieving credentials for req.params.provider:,{error error.message stack error.stack userId user.id},...)\n console.error(`❌ Error retrieving credentials for req.params.provider:,{error error.message stack error.stack userId user.id},...)\n', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = eff33338030980b677c057a60622d94db7e664722811d6c6ba2bd2731d8fddd819317420fbfe7babab4320d98171b6574559d5c63547462a91197e339aad2801_0
2025-06-06 23:13:33,712 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `❌ Error retrieving credentials for req.params.provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`❌ Error retrieving credentials for req.params.provider:,{error error.message stack error.stack userId user.id},...)\n console.error(`❌ Error retrieving credentials for req.params.provider:,{error error.message stack error.stack userId user.id},...)\n', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = eff33338030980b677c057a60622d94db7e664722811d6c6ba2bd2731d8fddd819317420fbfe7babab4320d98171b6574559d5c63547462a91197e339aad2801_0
2025-06-06 23:13:33,713 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `❌ Error retrieving credentials for req.params.provider: $UTIL = require(\'util\')\n...\n $UTIL.format(`❌ Error retrieving credentials for req.params.provider:,{error error.message stack error.stack userId user.id},...)\n console.error(`❌ Error retrieving credentials for req.params.provider:,{error error.message stack error.stack userId user.id},...)\n', PosixPath('server/routes/credentialsRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = eff33338030980b677c057a60622d94db7e664722811d6c6ba2bd2731d8fddd819317420fbfe7babab4320d98171b6574559d5c63547462a91197e339aad2801_0
2025-06-06 23:13:33,714 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ebf71f340ac9c8c239c05872f11306f6dbee70123ef74ab019c5cc08204f85915d352ea58aa7dd5a7a1bf4c2cd57e3f091135f739d5dc48872548fc9ceb74ff6_0
2025-06-06 23:13:33,715 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error downloading content for document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error downloading content for document docId:,downloadError,...)\n console.error(`Error downloading content for document docId:,downloadError,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e0a949512836e4ed99d6e8aefe9ad2f587f5413311e9573a2c016aae76021a1cb1865e1fd6d57dedfc666923be4049f8124b5cbfb81b9cb81217d32efcf9d6cc_0
2025-06-06 23:13:33,717 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error downloading content for document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error downloading content for document docId:,downloadError,...)\n console.error(`Error downloading content for document docId:,downloadError,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e0a949512836e4ed99d6e8aefe9ad2f587f5413311e9573a2c016aae76021a1cb1865e1fd6d57dedfc666923be4049f8124b5cbfb81b9cb81217d32efcf9d6cc_0
2025-06-06 23:13:33,718 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Error downloading content for document docId: $UTIL = require(\'util\')\n...\n $UTIL.format(`Error downloading content for document docId:,downloadError,...)\n console.error(`Error downloading content for document docId:,downloadError,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e0a949512836e4ed99d6e8aefe9ad2f587f5413311e9573a2c016aae76021a1cb1865e1fd6d57dedfc666923be4049f8124b5cbfb81b9cb81217d32efcf9d6cc_0
2025-06-06 23:13:33,719 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ebf71f340ac9c8c239c05872f11306f6dbee70123ef74ab019c5cc08204f85915d352ea58aa7dd5a7a1bf4c2cd57e3f091135f739d5dc48872548fc9ceb74ff6_0
2025-06-06 23:13:33,721 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n combinedTextContent + `Content from document.file_name:\\n docTextContent\\n\\n combinedTextContent.concat(`Content from document.file_name:\\n docTextContent\\n\\n) `...${...}...`\n combinedTextContent.concat("...")\n `Quiz Generation: AI generated aiGeneratedData.questions.length questions with types: $UTIL = require(\'util\')\n...\n $UTIL.format(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n console.log(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 792a2aeefd84f6db4ea621bf3d5a520b51c992b2d89512053ce92d7591d2c71223643a17430ba01900104f9f995eda19afef74c0b44f110dbea5f200da4e81bb_0
2025-06-06 23:13:33,722 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n combinedTextContent + `Content from document.file_name:\\n docTextContent\\n\\n combinedTextContent.concat(`Content from document.file_name:\\n docTextContent\\n\\n) `...${...}...`\n combinedTextContent.concat("...")\n `Quiz Generation: AI generated aiGeneratedData.questions.length questions with types: $UTIL = require(\'util\')\n...\n $UTIL.format(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n console.log(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 792a2aeefd84f6db4ea621bf3d5a520b51c992b2d89512053ce92d7591d2c71223643a17430ba01900104f9f995eda19afef74c0b44f110dbea5f200da4e81bb_0
2025-06-06 23:13:33,724 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n combinedTextContent + `Content from document.file_name:\\n docTextContent\\n\\n combinedTextContent.concat(`Content from document.file_name:\\n docTextContent\\n\\n) `...${...}...`\n combinedTextContent.concat("...")\n `Quiz Generation: AI generated aiGeneratedData.questions.length questions with types: $UTIL = require(\'util\')\n...\n $UTIL.format(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n console.log(`Quiz Generation: AI generated aiGeneratedData.questions.length questions with types:,typeCounts,...)\n', PosixPath('server/routes/quizRoutes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 792a2aeefd84f6db4ea621bf3d5a520b51c992b2d89512053ce92d7591d2c71223643a17430ba01900104f9f995eda19afef74c0b44f110dbea5f200da4e81bb_0
2025-06-06 23:13:33,724 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e3ac40a0e0e61cabaa8944aa579643082a4a77fb689f81c0fd9d7aa649523a226adcb6751dd032bdffdc157e19e62ca893744bd6f03e341d40f28d35afaa6fb5_0
2025-06-06 23:13:33,725 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Generating quiz \'quizName\' for document documentId, user userId, options: $UTIL = require(\'util\')\n...\n $UTIL.format(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n console.log(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 18e6ba934145c2d46fe4bca682c0df5192cae44838e34e2163571c014925daaa25c5ccbab490b845e2b8968888e66b49908031d4433ceef56b443fb0f8fe298d_0
2025-06-06 23:13:33,725 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Generating quiz \'quizName\' for document documentId, user userId, options: $UTIL = require(\'util\')\n...\n $UTIL.format(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n console.log(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 18e6ba934145c2d46fe4bca682c0df5192cae44838e34e2163571c014925daaa25c5ccbab490b845e2b8968888e66b49908031d4433ceef56b443fb0f8fe298d_0
2025-06-06 23:13:33,726 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `Generating quiz \'quizName\' for document documentId, user userId, options: $UTIL = require(\'util\')\n...\n $UTIL.format(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n console.log(`Generating quiz \'quizName\' for document documentId, user userId, options:,generationOptions,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 18e6ba934145c2d46fe4bca682c0df5192cae44838e34e2163571c014925daaa25c5ccbab490b845e2b8968888e66b49908031d4433ceef56b443fb0f8fe298d_0
2025-06-06 23:13:33,726 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e3ac40a0e0e61cabaa8944aa579643082a4a77fb689f81c0fd9d7aa649523a226adcb6751dd032bdffdc157e19e62ca893744bd6f03e341d40f28d35afaa6fb5_0
2025-06-06 23:13:33,727 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `OpenRouter API error: aiResponse.status aiResponse.statusText $UTIL = require(\'util\')\n...\n $UTIL.format(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n console.error(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = d85783d38cf827356591f5c95a04cfd3aeefd441a0ce032041e62f04f45fdbf92ba05c11ad1bcdef4516986f7840bebcecc4cf10f7bd5d31e7159e2b904cec26_0
2025-06-06 23:13:33,728 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `OpenRouter API error: aiResponse.status aiResponse.statusText $UTIL = require(\'util\')\n...\n $UTIL.format(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n console.error(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = d85783d38cf827356591f5c95a04cfd3aeefd441a0ce032041e62f04f45fdbf92ba05c11ad1bcdef4516986f7840bebcecc4cf10f7bd5d31e7159e2b904cec26_0
2025-06-06 23:13:33,728 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n `OpenRouter API error: aiResponse.status aiResponse.statusText $UTIL = require(\'util\')\n...\n $UTIL.format(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n console.error(`OpenRouter API error: aiResponse.status aiResponse.statusText,errorBody,...)\n', PosixPath('supabase/functions/generate-quiz-questions/index.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = d85783d38cf827356591f5c95a04cfd3aeefd441a0ce032041e62f04f45fdbf92ba05c11ad1bcdef4516986f7840bebcecc4cf10f7bd5d31e7159e2b904cec26_0
2025-06-06 23:13:33,731 - semgrep.core_runner - DEBUG - semgrep ran in 0:01:05.198307 on 232 files
2025-06-06 23:13:33,732 - semgrep.core_runner - DEBUG - findings summary: 4 warning, 9 error, 25 info
2025-06-06 23:13:33,736 - semgrep.app.auth - DEBUG - Getting API token from settings file
2025-06-06 23:13:33,736 - semgrep.app.auth - DEBUG - No API token found in settings file
2025-06-06 23:13:33,737 - semgrep.semgrep_core - DEBUG - Failed to open resource semgrep-core-proprietary: [Errno 2] No such file or directory: '/tmp/_MEIgZOH6x/semgrep/bin/semgrep-core-proprietary'.
2025-06-06 23:13:33,876 - semgrep.output - VERBOSE - 
========================================
Files skipped:
========================================

  Always skipped by Opengrep:

   • <none>

  Skipped by .gitignore:
  (Disable by passing --no-git-ignore)

   • <all files not listed by `git ls-files` were skipped>

  Skipped by .semgrepignore:
  - https://semgrep.dev/docs/ignoring-files-folders-code/#understand-semgrep-defaults

   • <none>

  Skipped by --include patterns:

   • <none>

  Skipped by --exclude patterns:

   • <none>

  Files skipped due to insufficient read permissions:

   • <none>

  Skipped by limiting to files smaller than 1000000 bytes:
  (Adjust with the --max-target-bytes flag)

   • generated-icon.png

  Partially analyzed due to parsing or internal Opengrep errors

   • server/debug/checkCredentials.ts
   • server/routes/flashcards.ts
   • server/routes/quizExpressRoutes.ts
   • tailwind.config.ts (1 lines skipped)

2025-06-06 23:13:33,877 - semgrep.output - INFO - Some files were skipped or only partially analyzed.
  Scan was limited to files tracked by git.
  Partially scanned: 4 files only partially analyzed due to parsing or internal Opengrep errors
  Scan skipped: 1 files larger than 1.0 MB
  For a full list of skipped files, run opengrep with the --verbose flag.

Ran 445 rules on 232 files: 38 findings.
2025-06-06 23:13:33,878 - semgrep.app.version - DEBUG - Version cache does not exist
2025-06-06 23:13:33,878 - semgrep.app.version - DEBUG - Version cache does not exist
2025-06-06 23:13:33,895 - semgrep.metrics - VERBOSE - Not sending pseudonymous metrics since metrics are configured to OFF and registry usage is False
