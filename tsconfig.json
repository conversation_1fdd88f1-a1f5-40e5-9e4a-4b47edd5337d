{"include": ["client/src/**/*", "shared/**/*", "server/**/*"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "target": "ES2020", "module": "ESNext", "strict": true, "lib": ["ES2020", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node", "vite/client", "jest"], "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"]}}}