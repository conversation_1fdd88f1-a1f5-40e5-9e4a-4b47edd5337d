import express from "express";
import path from "path";
import { fileURLToPath } from "url";
import cors from "cors";
import { createProxyMiddleware } from "http-proxy-middleware";

// Get current file's directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// Load environment variables only in development
if (process.env.NODE_ENV !== "production") {
  const { default: dotenv } = await import("dotenv");
  dotenv.config({ path: path.resolve(__dirname, "../.env") });
}

// Security headers middleware
app.use((req, res, next) => {
  // Security headers
  res.setHeader("X-Content-Type-Options", "nosniff");
  res.setHeader("X-Frame-Options", "DENY");
  res.setHeader("X-XSS-Protection", "1; mode=block");
  res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
  res.setHeader(
    "Content-Security-Policy",
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://replit.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss: http://localhost:5000 https:;"
  );
  
  next();
});

// CORS configuration for API requests to backend
app.use(
  cors({
    origin: process.env.NODE_ENV === "production" 
      ? true 
      : ["http://localhost:3000", "http://localhost:5000"],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Parse JSON bodies
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: false }));

// API proxy to backend server
const backendUrl = process.env.BACKEND_URL || `http://localhost:${process.env.PORT || "5000"}`;
console.log(`Proxying API requests to: ${backendUrl}`);

app.use('/api', createProxyMiddleware({
  target: backendUrl,
  changeOrigin: true,
  secure: false,
  timeout: 30000,
  proxyTimeout: 30000,
  onError: (err, req, res) => {
    console.error('Proxy error:', err.message);
    res.status(500).json({
      error: 'Backend service unavailable',
      message: 'Unable to connect to API server'
    });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`[PROXY] ${req.method} ${req.url} -> ${backendUrl}${req.url}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`[PROXY] ${req.method} ${req.url} <- ${proxyRes.statusCode}`);
  }
}));

// Serve static files from the Vite build output
const distPath = path.resolve(__dirname, "../dist/public");
console.log(`Serving static files from: ${distPath}`);

// Static file serving with proper caching headers
app.use(express.static(distPath, {
  maxAge: process.env.NODE_ENV === "production" ? "1y" : "0",
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    // Cache static assets for a year in production
    if (process.env.NODE_ENV === "production") {
      if (filePath.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
        res.setHeader("Cache-Control", "public, max-age=31536000, immutable");
      } else if (filePath.endsWith("index.html")) {
        res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
      }
    }
  }
}));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({ 
    status: "healthy", 
    service: "frontend-server",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development"
  });
});

// SPA fallback - serve index.html for all non-API routes
app.get("*", (req, res) => {
  // API routes are handled by the proxy middleware above
  // This fallback only handles frontend routes
  const indexPath = path.join(distPath, "index.html");
  res.sendFile(indexPath, (err) => {
    if (err) {
      console.error("Error serving index.html:", err);
      res.status(500).json({ error: "Failed to serve application" });
    }
  });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error("Frontend server error:", err);
  res.status(500).json({ 
    error: "Internal server error",
    message: process.env.NODE_ENV === "development" ? err.message : "Something went wrong"
  });
});

// Start the frontend server
const port = parseInt(process.env.FRONTEND_PORT || "3000", 10);
const host = "0.0.0.0";

app.listen(port, host, () => {
  console.log(`✅ Frontend server running on port ${port}`);
  console.log(`🌐 Frontend available at: http://localhost:${port}`);
  console.log(`🔍 Frontend health check: http://localhost:${port}/health`);
  console.log(`📁 Serving static files from: ${distPath}`);
});

// Handle graceful shutdown
const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
signals.forEach((signal) => {
  process.on(signal, () => {
    console.log(`Frontend server received ${signal}, shutting down gracefully...`);
    process.exit(0);
  });
});
