import express from 'express';
import request from 'supertest';
import aiRoutes from '../routes/aiRoutes';

// Mock axios to avoid real HTTP requests
import axios from 'axios';
jest.mock('axios');

// Mock supabase middleware dynamic import
jest.mock('../middleware/supabaseMiddleware', () => ({
  __esModule: true,
  supabaseClient: {
    auth: {
      getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'user1' } }, error: null })
    }
  }
}));

// Mock API key storage
jest.mock('../middleware/apiKeyStorage', () => ({
  __esModule: true,
  getEphemeralUserCredentials: jest.fn().mockResolvedValue({
    success: true,
    credentials: {
      apiKey: 'mock-key',
      baseUrl: 'https://mockapi.com',
      generationModel: 'mock-model',
      extractionModel: 'mock-model'
    }
  })
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('AI Routes', () => {
  const app = express();
  app.use(express.json());
  app.use('/api', aiRoutes);

  beforeEach(() => {
    mockedAxios.post.mockReset();
  });

  it('POST /api/flashcards/generate returns generated flashcards', async () => {
    mockedAxios.post.mockResolvedValue({
      data: { choices: [ { message: { content: JSON.stringify({ flashcards: [{ question: 'q', answer: 'a' }] }) } } ] }
    });

    const res = await request(app)
      .post('/api/flashcards/generate')
      .set('Authorization', 'Bearer testtoken')
      .send({ textContent: 'text', documentId: 'doc', count: 1 });

    expect(res.status).toBe(201);
    expect(res.body.flashcards).toHaveLength(1);
  });

  it('POST /api/flashcards/generate validates request body', async () => {
    const res = await request(app)
      .post('/api/flashcards/generate')
      .set('Authorization', 'Bearer testtoken')
      .send({});

    expect(res.status).toBe(400);
    expect(res.body.error).toMatch(/Missing required fields/);
  });

  it('POST /api/flashcards/generate requires authentication', async () => {
    const res = await request(app)
      .post('/api/flashcards/generate')
      .send({ textContent: 'text', documentId: 'doc' });

    expect(res.status).toBe(401);
  });

  it('POST /api/extract-and-format returns formatted content', async () => {
    mockedAxios.post.mockResolvedValue({
      data: {
        choices: [ { message: { content: '# Title\nBody' } } ]
      }
    });

    const res = await request(app)
      .post('/api/extract-and-format')
      .set('Authorization', 'Bearer testtoken')
      .send({ rawText: 'raw', fileName: 'file.txt' });

    expect(res.status).toBe(200);
    expect(res.body.formattedContent).toBe('# Title\nBody');
  });
});
