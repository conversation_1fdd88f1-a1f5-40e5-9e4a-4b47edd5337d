import express from 'express';
import request from 'supertest';
import testRoutes from '../routes/testRoutes';

describe('GET /api/test', () => {
  const app = express();
  app.use('/api', testRoutes);

  it('responds with success message', async () => {
    const res = await request(app).get('/api/test');
    expect(res.status).toBe(200);
    expect(res.body.message).toMatch(/Backend connection successful/);
    expect(res.body).toHaveProperty('environment');
  });
});
