// Load environment variables FIRST, before any other imports
import path from "path";
import { fileURLToPath } from "url";
import { config as dotenvConfig } from "dotenv";

// Get current file's directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables immediately in development
if (process.env.NODE_ENV !== "production") {
  const envPath = path.resolve(__dirname, "../.env");
  dotenvConfig({ path: envPath });
}

import express, { type Request, Response, NextFunction } from "express";
import cors from "cors";
import { Readable } from "stream"; // Import Readable

// Extend RequestInit to include Node.js specific duplex option
interface NodeRequestInit extends RequestInit {
  duplex?: "half" | "full";
}

// Route imports moved to after environment loading

const app = express();

// Disable Express's ETag headers for API routes to avoid 304 responses
app.disable("etag");

// Ensure dynamic API responses are never cached
app.use((req, res, next) => {
  if (req.path.startsWith("/api")) {
    res.setHeader("Cache-Control", "no-store");
  }
  next();
});

// CORS configuration for frontend-backend separation
app.use(
  cors({
    // Allow requests from the frontend server
    origin: process.env.NODE_ENV === "production"
      ? ["http://localhost:3000", process.env.FRONTEND_URL].filter(Boolean)
      : ["http://localhost:3000", "http://localhost:5173", process.env.FRONTEND_URL].filter(Boolean),
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: false }));

// Debug middleware to log API requests
app.use((req, res, next) => {
  if (req.path.startsWith("/api")) {
    console.log(
      `${req.method} ${req.path} - Body keys: ${Object.keys(req.body).join(
        ", "
      )}`
    );
  }
  next();
});

// Serve static files in production - moved to async function below

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      console.log(logLine);
    }
  });

  next();
});

// Route mounting moved to async function after environment loading

// Helper function to convert Node.js stream to Web stream if needed
async function nodeStreamToWebStream(
  nodeStream: NodeJS.ReadableStream
): Promise<ReadableStream<Uint8Array>> {
  return new ReadableStream({
    start(controller) {
      nodeStream.on("data", (chunk) =>
        controller.enqueue(new Uint8Array(chunk))
      );
      nodeStream.on("end", () => controller.close());
      nodeStream.on("error", (err) => controller.error(err));
    },
  });
}

// Adapter middleware for Hono to Express
const honoAdapter = (honoApp: any, basePath: string = "") => {
  return async (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    try {
      const hasBody = req.method !== "GET" && req.method !== "HEAD" && req.body;
      const requestBody = hasBody
        ? req.body instanceof Readable
          ? await nodeStreamToWebStream(req.body)
          : new ReadableStream({
              start: (controller) => {
                controller.enqueue(Buffer.from(JSON.stringify(req.body)));
                controller.close();
              },
            })
        : undefined;

      // Strip the base path from the URL for Hono
      let honoPath = req.url;
      if (basePath && req.url.startsWith(basePath)) {
        honoPath = req.url.substring(basePath.length) || "/";
      }

      const webRequest = new Request(
        `${req.protocol}://${req.get("host")}${honoPath}`,
        {
          method: req.method,
          headers: new Headers(req.headers as HeadersInit),
          body: requestBody,
          // Required for Node.js 18+ when sending a body
          duplex: hasBody ? "half" : undefined,
        } as NodeRequestInit
      );

      console.log(`[honoAdapter] Forwarding ${webRequest.method} ${webRequest.url} to Hono app. Path for Hono: ${honoPath}`);
      const webResponse = await honoApp.fetch(webRequest);

      res.status(webResponse.status);
      webResponse.headers.forEach((value: string, key: string) => {
        res.setHeader(key, value);
      });

      if (webResponse.body) {
        const reader = webResponse.body.getReader();
        const forwardStream = async () => {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            res.write(value);
          }
          res.end();
        };
        await forwardStream();
      } else {
        res.end();
      }
    } catch (error) {
      next(error);
    }
  };
};

// All route mounting moved to async function after environment loading

(async () => {
  console.log("🚀 Starting server initialization...");

  // Environment variables already loaded at the top of the file

  // Now import routes after environment variables are loaded
  const { registerRoutes } = await import("./routes");
  const { log } = await import("./vite");
  const aiRoutes = (await import("./routes/aiRoutes")).default;
  const quizRoutes = (await import("./routes/quizRoutes")).default;
  const documentRoutes = (await import("./routes/documentRoutes")).default;
  const flashcardDeckRoutes = (await import("./routes/flashcardDeckRoutes")).default;
  const flashcardRoutes = (await import("./routes/flashcardRoutes")).default;
  const flashcardSetRoutes = (await import("./routes/flashcardSetRoutes")).default;
  const testRoutes = (await import("./routes/testRoutes")).default;
  const healthRoutes = (await import("./routes/healthRoutes")).default;
  const credentialsRoutes = (await import("./routes/credentialsRoutes")).default;
  const authRoutes = (await import("./routes/authRoutes")).default;

  // Mount routes after imports
  console.log("🔧 Mounting AI routes at /api");
  app.use("/api", aiRoutes);
  console.log("🔧 Mounting flashcard deck routes at /api/decks");
  app.use("/api/decks", flashcardDeckRoutes);
  console.log("🔧 Mounting test routes at /api");
  app.use("/api", testRoutes);
  console.log("🔧 Mounting health check routes at /api/health");
  app.use("/api/health", healthRoutes);
  console.log("🔧 Mounting credentials routes at /api/credentials");
  app.use("/api/credentials", credentialsRoutes);
  console.log("🔧 Mounting auth routes at /api/auth");
  app.use("/api/auth", authRoutes);

  // Mount Express routes directly
  console.log("🔧 Mounting document routes at /api/documents");
  app.use("/api/documents", documentRoutes);

  // Mount Hono routes with the adapter
  console.log("🔧 Mounting quiz routes at /api/quizzes");
  app.use("/api/quizzes", honoAdapter(quizRoutes, "/api/quizzes"));

  console.log("🔧 Mounting flashcard set routes at /api/flashcard-sets");
  app.use("/api/flashcard-sets", honoAdapter(flashcardSetRoutes, "/api/flashcard-sets"));

  console.log("🔧 Mounting individual flashcard routes at /api/flashcards");
  app.use("/api/flashcards", honoAdapter(flashcardRoutes, "/api/flashcards"));

  try {

    const server = await registerRoutes(app);
    console.log("✅ Routes registered successfully");

    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
      console.error("Unhandled error in Main Express App:", err);
      const status = err.status || err.statusCode || 500;
      const responseBody: { error: string; message: string; stack?: string } = {
        error: "An unexpected server error occurred.",
        message: err.message || "Internal Server Error",
      };
      // Optionally include stack in development
      if (app.get("env") === "development" && err.stack) {
        responseBody.stack = err.stack;
      }
      res.status(status).json(responseBody);
      // Error is logged and JSON response sent. Do not call _next(err) or throw err to prevent Vite's HTML error.
    });



    // Get port from environment variable or use default
    // In production (Replit deployment), use port 80
    // In development, use port 5000
    const port = parseInt(process.env.PORT || "5000", 10);
    const isWindows = process.platform === "win32";

    log(`Starting server in ${process.env.NODE_ENV || 'development'} mode on port ${port}`);

    // Log environment info for debugging
    log(`Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}`);
    log(`API Base URL will be: ${process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api'}`);

    const httpServer = server.listen(
      {
        port,
        host: "0.0.0.0",
        ...(isWindows ? {} : { reusePort: true }),
      },
      () => {
        log(`✅ Backend server running on port ${port}`);
        log(`🌐 API available at: http://localhost:${port}/api`);
        log(`🔍 Health check: http://localhost:${port}/api/health`);
        log(`🐛 Debug routes: http://localhost:${port}/api/debug/routes`);
      }
    );
    
    // Handle graceful shutdown
    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
    signals.forEach((signal) => {
      process.on(signal, () => {
        log(`Received ${signal}, gracefully shutting down...`);
        
        // Set a timeout for forceful shutdown if graceful shutdown takes too long
        const forcefulShutdownTimeout = setTimeout(() => {
          log('Forceful shutdown timeout reached, exiting immediately!');
          process.exit(1);
        }, 30000); // 30 seconds timeout
        
        // Attempt graceful shutdown
        httpServer.close(() => {
          log('HTTP server closed successfully.');
          clearTimeout(forcefulShutdownTimeout);
          process.exit(0);
        });
      });
    });
  } catch (error: any) {
    console.error("❌ Failed to start server:", error);
    console.error("Error details:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  }
})();
