import { type Express } from "express";



export function log(message: string) {
  const now = new Date();
  const timeStr = now.toLocaleTimeString();
  console.log(`${timeStr} [express] ${message}`);
}

// Note: Vite dev middleware has been moved to the dedicated frontend development setup
// The backend server no longer handles frontend concerns
export async function setupVite(app: Express) {
  log("Vite middleware is no longer used in the backend server");
  log("Frontend development is handled by the Vite dev server on port 3000");
  log("Backend server is focused on API endpoints only");
}

// Note: Static file serving has been moved to the dedicated frontend server
// This function is kept for backward compatibility but does nothing
export function serveStatic(app: Express) {
  log("Static file serving is now handled by the dedicated frontend server");
  log("Backend server is focused on API endpoints only");
}
