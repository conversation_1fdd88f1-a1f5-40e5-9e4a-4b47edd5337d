#!/usr/bin/env bash
set -euo pipefail

echo "🏗️  Starting ChewyAI build process..."

# Clean previous build
echo "🧹 Cleaning previous build..."
npm run build:clean

# Build client (frontend)
echo "🌐 Building client..."
npm run build:client

# Build server (backend)
echo "🔧 Building server..."
npm run build:server

# Build frontend server
echo "📡 Building frontend server..."
npm run build:frontend

# Verify build files
echo "✅ Verifying build files..."

if [ ! -f "dist/index.js" ]; then
  echo "❌ Backend build failed - dist/index.js not found"
  exit 1
fi

if [ ! -f "dist/frontend-server.js" ]; then
  echo "❌ Frontend server build failed - dist/frontend-server.js not found"
  exit 1
fi

if [ ! -d "dist/public" ]; then
  echo "❌ Client build failed - dist/public directory not found"
  exit 1
fi

echo "🎉 Build completed successfully!"
echo "📁 Build artifacts:"
ls -la dist/