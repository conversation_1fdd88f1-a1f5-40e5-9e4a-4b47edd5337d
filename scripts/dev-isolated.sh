#!/usr/bin/env bash
set -euo pipefail

echo "🚀 Starting ChewyAI in isolated development mode..."
echo "This mode runs the frontend server (port 3000) and backend server (port 5000) separately"
echo "Similar to production but with development builds"

# Check if build files exist
if [ ! -d "dist/public" ]; then
  echo "❌ Frontend build not found. Building client..."
  npm run build:client
fi

if [ ! -f "dist/frontend-server.js" ]; then
  echo "❌ Frontend server build not found. Building frontend server..."
  npm run build:frontend
fi

echo "✅ All build files ready"

# Set development environment
export NODE_ENV=development

# Start both servers with concurrently
echo "🌐 Starting frontend server on port 3000..."
echo "🔧 Starting backend server on port 5000..."

concurrently \
  --names "FRONTEND,BACKEND" \
  --prefix-colors "cyan,green" \
  --kill-others \
  "npm run dev:frontend" \
  "npm run dev:server"
