#!/usr/bin/env bash
set -euo pipefail

echo "🔍 Verifying ChewyAI Architecture Isolation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Check build artifacts
echo "📦 Checking build artifacts..."

if [ -f "dist/index.js" ]; then
    print_status 0 "Backend server build exists"
else
    print_status 1 "Backend server build missing"
fi

if [ -f "dist/frontend-server.js" ]; then
    print_status 0 "Frontend server build exists"
else
    print_status 1 "Frontend server build missing"
fi

if [ -d "dist/public" ]; then
    print_status 0 "Frontend client build exists"
else
    print_status 1 "Frontend client build missing"
fi

if [ -f "dist/public/index.html" ]; then
    print_status 0 "Frontend index.html exists"
else
    print_status 1 "Frontend index.html missing"
fi

# Check configuration files
echo ""
echo "⚙️  Checking configuration files..."

if [ -f "frontend-server/index.ts" ]; then
    print_status 0 "Frontend server source exists"
else
    print_status 1 "Frontend server source missing"
fi

if [ -f ".replit" ]; then
    print_status 0 ".replit configuration exists"
else
    print_status 1 ".replit configuration missing"
fi

# Check package.json scripts
echo ""
echo "📜 Checking package.json scripts..."

if grep -q "dev:frontend" package.json; then
    print_status 0 "dev:frontend script exists"
else
    print_status 1 "dev:frontend script missing"
fi

if grep -q "build:frontend" package.json; then
    print_status 0 "build:frontend script exists"
else
    print_status 1 "build:frontend script missing"
fi

if grep -q "start:frontend" package.json; then
    print_status 0 "start:frontend script exists"
else
    print_status 1 "start:frontend script missing"
fi

if grep -q "start:backend" package.json; then
    print_status 0 "start:backend script exists"
else
    print_status 1 "start:backend script missing"
fi

# Check documentation
echo ""
echo "📚 Checking documentation..."

if [ -f "docs/ARCHITECTURE.md" ]; then
    print_status 0 "Architecture documentation exists"
else
    print_status 1 "Architecture documentation missing"
fi

# Test frontend server (without dependencies)
echo ""
echo "🧪 Testing frontend server..."

print_info "Starting frontend server for health check..."
timeout 3s node dist/frontend-server.js > /tmp/frontend-test.log 2>&1 &
FRONTEND_PID=$!
sleep 1

if kill -0 $FRONTEND_PID 2>/dev/null; then
    print_status 0 "Frontend server starts successfully"
    kill $FRONTEND_PID 2>/dev/null || true
else
    print_status 1 "Frontend server failed to start"
    cat /tmp/frontend-test.log
fi

# Check file structure
echo ""
echo "📁 Checking file structure..."

EXPECTED_FILES=(
    "frontend-server/index.ts"
    "scripts/start-production.sh"
    "scripts/dev-isolated.sh"
    "docs/ARCHITECTURE.md"
)

for file in "${EXPECTED_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status 0 "$file exists"
    else
        print_status 1 "$file missing"
    fi
done

echo ""
echo "🎉 Architecture verification complete!"
echo ""
print_info "Frontend Server: Serves static files and proxies API requests (port 3000)"
print_info "Backend Server: Handles API endpoints only (port 5000)"
print_info "Development: npm run dev (runs both Vite dev server and backend)"
print_info "Production: npm run start (runs both frontend server and backend)"
print_info "Build: npm run build (builds client, server, and frontend server)"
