# Deployment Connection Issue Fix

## Problem Summary

The application was experiencing "dial tcp 127.0.0.1:3000: connect: connection refused" errors during deployment. The build process completed successfully, but the runtime proxy connections were failing.

## Root Cause Analysis

### 1. Backend URL Resolution Issue
- **Problem**: Frontend server was incorrectly calculating backend URL
- **Cause**: Logic in `frontend-server/index.ts` was using `process.env.PORT` (which is 5000) but defaulting to port 3000
- **Result**: Frontend server tried to proxy to `http://localhost:3000` instead of `http://localhost:5000`

### 2. Deployment Configuration Mismatch
- **Problem**: `BACKEND_URL` in `.replit` was set to external URL `https://chewy-ai.replit.app:5000`
- **Cause**: In containerized environments, services should communicate internally via localhost
- **Result**: External URL caused connection issues in deployment environment

### 3. Service Startup Order
- **Problem**: Frontend server started before backend server
- **Cause**: Concurrently command started frontend first
- **Result**: Frontend server tried to connect to backend before it was ready

## Implemented Fixes

### 1. Enhanced Backend URL Resolution (`frontend-server/index.ts`)

```typescript
// Before (problematic)
const backendUrl = process.env.BACKEND_URL || `http://localhost:${process.env.PORT || "5000"}`;

// After (fixed)
let backendUrl: string;
if (process.env.BACKEND_URL) {
  backendUrl = process.env.BACKEND_URL;
} else if (process.env.NODE_ENV === "production") {
  const backendPort = process.env.PORT || "5000";
  backendUrl = `http://localhost:${backendPort}`;
} else {
  backendUrl = "http://localhost:5000";
}
```

### 2. Updated Deployment Configuration (`.replit`)

```toml
# Before
BACKEND_URL = "https://chewy-ai.replit.app:5000"

# After
BACKEND_URL = "http://localhost:5000"
```

### 3. Improved Error Handling and Logging

- Added comprehensive error logging with environment details
- Added backend connectivity check on startup
- Enhanced proxy error messages with debugging information

### 4. Fixed Service Startup Order

```bash
# Before
concurrently "node dist/frontend-server.js" "node dist/index.js"

# After  
concurrently "node dist/index.js" "node dist/frontend-server.js"
```

## Environment Variable Configuration

### Development
```bash
NODE_ENV=development
PORT=5000
FRONTEND_PORT=3000
# BACKEND_URL not needed (defaults to http://localhost:5000)
```

### Production (Replit)
```bash
NODE_ENV=production
PORT=5000
FRONTEND_PORT=80
BACKEND_URL=http://localhost:5000
VITE_API_BASE_URL=https://chewy-ai.replit.app/api
```

## Verification Steps

1. **Build Verification**
   ```bash
   npm run build
   # Check that dist/index.js and dist/frontend-server.js exist
   ```

2. **Local Production Test**
   ```bash
   npm run preview
   # Should start both servers without connection errors
   ```

3. **Health Check Verification**
   ```bash
   curl http://localhost:3000/health  # Frontend health
   curl http://localhost:5000/api/health  # Backend health
   ```

## Monitoring and Debugging

### Log Messages to Watch For

**Success Indicators:**
- `✅ Frontend server running on port 80`
- `✅ Backend server running on port 5000`
- `✅ Backend is reachable at http://localhost:5000`
- `🔗 Proxying API requests to: http://localhost:5000`

**Error Indicators:**
- `❌ Proxy error: connect ECONNREFUSED`
- `⚠️ Backend not immediately available`
- `dial tcp 127.0.0.1:3000: connect: connection refused`

### Debugging Commands

```bash
# Check running processes
ps aux | grep node

# Check port usage
netstat -tulpn | grep :5000
netstat -tulpn | grep :80

# Test backend directly
curl http://localhost:5000/api/health

# Check environment variables
env | grep -E "(NODE_ENV|PORT|BACKEND_URL)"
```

## Prevention Strategies

1. **Environment Variable Validation**: Added startup checks for required environment variables
2. **Service Health Checks**: Added backend connectivity verification on frontend startup
3. **Comprehensive Logging**: Enhanced error messages with environment context
4. **Startup Order**: Ensured backend starts before frontend
5. **Documentation**: Clear environment variable configuration guide

## Related Files Modified

- `frontend-server/index.ts` - Fixed backend URL resolution and added error handling
- `.replit` - Updated deployment environment variables
- `package.json` - Fixed service startup order
- `scripts/start-production.sh` - Updated startup order
- `scripts/dev-isolated.sh` - Updated startup order for consistency
