# ChewyAI Deployment Guide

## 🚀 Production Deployment on Replit

### Prerequisites
- Replit account with deployment capabilities
- Supabase project configured
- Environment variables prepared

### Deployment Steps

#### 1. Environment Configuration
Set the following environment variables in Replit Secrets or your `.env` file:

```bash
# Required for Production
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
NODE_ENV=production
PORT=80
```

For local development copy `.env.example` to `.env` and define the same
variables (including `VITE_DATABASE_PASSWORD` if used). The server scripts rely
on these environment variables instead of hard coded values.

#### 2. Build Process
The deployment uses the following build command:
```bash
npm run build
```

This will:
1. Clean the `dist` directory
2. Build the React frontend with production optimizations
3. Bundle the Express.js server with minification

#### 3. Production Server
The production server runs:
```bash
NODE_ENV=production node dist/index.js
```

### Build Configuration

#### Frontend Build (Vite)
- **Output**: `dist/public/`
- **Optimizations**: 
  - Code splitting for better caching
  - Asset hashing for cache busting
  - Minification with esbuild
  - Source maps disabled in production

#### Backend Build (esbuild)
- **Output**: `dist/index.js`
- **Optimizations**:
  - Bundle all dependencies
  - Minification enabled
  - External packages for Node.js modules

## 🔧 Local Development

### Setup
```bash
# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Configure your environment variables in .env

# Start development servers
npm run dev
```

### Development Scripts
- `npm run dev` - Start both frontend and backend in development mode
- `npm run dev:client` - Start only the Vite dev server (port 3000)
- `npm run dev:server` - Start only the Express server (port 5000)

## 🏗️ Build Process Details

### Production Build Steps
1. **Clean**: Remove existing `dist` directory
2. **Client Build**: 
   - Vite builds React app to `dist/public/`
   - Optimizes assets and creates manifest
   - Generates hashed filenames for caching
3. **Server Build**:
   - esbuild bundles server code to `dist/index.js`
   - Minifies and optimizes for production

### Build Verification
```bash
# Test the build process
npm run test:build

# Preview production build locally
npm run preview
```

## 🌐 Static File Serving

### Production Configuration
- **Static Files**: Served from `dist/public/` with optimized caching
- **SPA Routing**: All non-API routes serve `index.html`
- **Cache Headers**: 
  - HTML files: No cache
  - Assets (JS/CSS): 1 year cache with immutable flag
- **Security Headers**: CSP, XSS protection, frame options

### API Routes
All API endpoints are prefixed with `/api` and handled by Express:
- `/api/health` - Health check endpoint
- `/api/flashcards/*` - Flashcard management
- `/api/quizzes/*` - Quiz functionality
- `/api/documents/*` - Document processing
- `/api/ai/*` - AI integration endpoints

## 🔍 Monitoring & Health Checks

### Health Check Endpoint
- **URL**: `/api/health`
- **Method**: GET
- **Response**: JSON with status and timestamp
- **Use**: Replit deployment monitoring

### Logging
- Request/response logging for API endpoints
- Error logging with appropriate detail levels
- No sensitive data in logs (API keys, tokens)

## 🚨 Troubleshooting

### Common Issues

#### Build Failures
- **Missing Dependencies**: Run `npm install`
- **TypeScript Errors**: Run `npm run check`
- **Environment Variables**: Verify all required vars are set

#### Runtime Issues
- **Static Files Not Found**: Ensure build completed successfully
- **API Errors**: Check server logs and environment configuration
- **Authentication Issues**: Verify Supabase configuration

#### Performance Issues
- **Slow Loading**: Check bundle sizes and optimize imports
- **Memory Issues**: Monitor server resource usage
- **Cache Issues**: Verify cache headers and asset hashing

### Debug Commands
```bash
# Check TypeScript
npm run check

# Test build process
npm run test:build

# View build output
ls -la dist/
ls -la dist/public/

# Check environment
npm run dev:server
# Then visit http://localhost:5000/api/debug/env
```

## 📊 Performance Optimization

### Frontend Optimizations
- Code splitting by route and vendor libraries
- Asset optimization and compression
- Lazy loading for non-critical components
- Optimized bundle sizes with tree shaking

### Backend Optimizations
- Minified server bundle
- Efficient static file serving
- Proper caching headers
- Gzip compression (handled by Replit)

### Database Optimizations
- Supabase connection pooling
- Efficient queries with proper indexing
- Row Level Security for data access control

## 🔄 Deployment Updates

### Rolling Updates
1. Update code in Replit
2. Replit automatically triggers build
3. Health check validates deployment
4. Traffic switches to new version

### Rollback Procedure
1. Revert code changes in Replit
2. Redeploy previous version
3. Verify health check passes
4. Monitor application functionality
