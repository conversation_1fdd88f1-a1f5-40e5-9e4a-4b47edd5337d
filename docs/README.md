# ChewyAI Documentation

Welcome to the ChewyAI documentation. This folder contains comprehensive documentation for the ChewyAI full-stack application.

## 📁 Documentation Structure

- **[ARCHITECTURE.md](./ARCHITECTURE.md)** - Application architecture and service isolation
- **[RULES.md](./RULES.md)** - Development rules, coding standards, and best practices
- **[MEMORIES.md](./MEMORIES.md)** - Important system decisions and architectural choices
- **[DEPLOYMENT.md](./DEPLOYMENT.md)** - Production deployment guide and configuration
- **[SECURITY.md](./SECURITY.md)** - Security practices and API key management
- **[API.md](./API.md)** - API documentation and endpoint reference
- **[TROUBLESHOOTING.md](./TROUBLESHOOTING.md)** - Common issues and debugging guide

## 🚀 Quick Start

### Development
```bash
npm run dev
```

Copy `.env.example` to `.env` and provide values for `SUPABASE_URL`,
`SUPABASE_SERVICE_ROLE_KEY`, and any other secrets before running the
development scripts.

### Production Build
```bash
npm run build
npm run start
```

### Testing Build
```bash
npm run test:build
```

### Continuous Integration
Pull requests created by **openai-codex** run through an automated test
workflow. If the tests succeed, the workflow automatically merges the pull
request. The workflow definition lives in
`.github/workflows/test-and-automerge.yml`.

## 🏗️ Architecture Overview

ChewyAI uses a completely isolated frontend and backend architecture:

- **Frontend Server**: Express.js serving React build (port 3000)
- **Backend API**: Express.js + Node.js API server (port 5000)
- **Client**: React + Vite + TypeScript + Tailwindcss
- **Database**: Supabase (PostgreSQL)
- **AI Integration**: OpenRouter API (user-configured)
- **Deployment**: Replit (production-ready)

For detailed architecture information, see [ARCHITECTURE.md](./ARCHITECTURE.md).

## 🔐 Security

- All API keys are handled ephemerally by the backend
- No sensitive credentials are exposed to the client
- Proper CORS and security headers implemented
- Environment variables used for all configuration

## 📝 Contributing

Please read [RULES.md](./RULES.md) before contributing to understand our development standards and practices.
