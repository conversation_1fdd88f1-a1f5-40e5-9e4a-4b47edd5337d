# ChewyAI Architecture

## Overview

ChewyAI uses a completely isolated frontend and backend architecture for better scalability, security, and deployment flexibility.

## Architecture Components

### Frontend Server (`frontend-server/`)
- **Purpose**: Serves the built React application and handles static file serving
- **Port**: 3000 (production and development)
- **Technology**: Express.js
- **Responsibilities**:
  - Serve static files from Vite build output
  - Handle SPA routing fallback
  - Implement security headers
  - Provide frontend health checks

### Backend Server (`server/`)
- **Purpose**: API server for all backend functionality
- **Port**: 5000 (production and development)
- **Technology**: Express.js with Hono for some routes
- **Responsibilities**:
  - Handle all API endpoints (`/api/*`)
  - Database operations
  - AI integrations
  - Authentication and authorization

### Client Application (`client/`)
- **Purpose**: React frontend application
- **Technology**: React + TypeScript + Vite + Tailwind CSS
- **Build Output**: `dist/public/`

## Development Environment

### Starting Development Servers
```bash
npm run dev
```
This starts:
- Vite dev server on port 3000 (with HMR)
- Backend API server on port 5000

### Individual Server Commands
```bash
# Frontend only (Vite dev server)
npm run dev:client

# Backend only
npm run dev:server
```

## Production Environment

### Building for Production
```bash
npm run build
```
This builds:
- React application → `dist/public/`
- Backend server → `dist/index.js`
- Frontend server → `dist/frontend-server.js`

### Starting Production Servers
```bash
npm run start
```
This starts both servers using concurrently:
- Frontend server on port 3000
- Backend server on port 5000

### Individual Production Commands
```bash
# Frontend server only
npm run start:frontend

# Backend server only
npm run start:backend
```

## Port Configuration

| Service | Development | Production |
|---------|-------------|------------|
| Frontend (Vite) | 3000 | - |
| Frontend Server | - | 3000 |
| Backend API | 5000 | 5000 |

## Environment Variables

### Development (.env)
```bash
PORT=5000
FRONTEND_PORT=3000
VITE_API_BASE_URL=http://localhost:5000/api
```

### Production
```bash
NODE_ENV=production
PORT=5000
FRONTEND_PORT=3000
VITE_API_BASE_URL=http://localhost:5000/api
```

## API Communication

- **Development**: Frontend (port 3000) → Backend (port 5000)
- **Production**: Frontend Server (port 3000) → Backend (port 5000)

The frontend server acts as a static file server and does not proxy API requests. All API calls go directly from the browser to the backend server.

## Security Features

### Frontend Server
- Content Security Policy (CSP)
- Security headers (X-Frame-Options, X-Content-Type-Options, etc.)
- Static file caching with proper cache headers
- SPA routing protection

### Backend Server
- CORS configuration for frontend origins
- API-specific security middleware
- Request validation and sanitization

## Health Checks

- **Frontend**: `GET /health`
- **Backend**: `GET /api/health`

## Deployment

### Replit Configuration
The `.replit` file is configured for the isolated architecture:
- Port 3000 mapped to external port 80
- Health check on `/health` (frontend)
- Concurrent server startup in production

### Docker Support
Individual Dockerfiles are available for:
- Frontend server (`client/Dockerfile.prod`)
- Backend server (`server/Dockerfile.prod`)

## Benefits of This Architecture

1. **Separation of Concerns**: Clear boundaries between frontend and backend
2. **Scalability**: Services can be scaled independently
3. **Security**: Better security isolation and configuration
4. **Deployment Flexibility**: Can deploy services separately or together
5. **Development Experience**: Clear development workflow with proper tooling
6. **Performance**: Optimized static file serving and caching

## Verification

To verify the architecture is working correctly, run:

```bash
bash scripts/verify-architecture.sh
```

This script checks:
- Build artifacts are present
- Configuration files are correct
- Package.json scripts are updated
- Documentation exists
- Frontend server starts successfully
- File structure is complete

## Migration Notes

This architecture represents a complete refactoring from the previous monolithic setup where the backend server handled both API endpoints and static file serving. The new isolated architecture provides:

- **Better Performance**: Dedicated frontend server optimized for static file serving
- **Enhanced Security**: Separate security configurations for frontend and backend
- **Improved Maintainability**: Clear service boundaries and responsibilities
- **Production Ready**: Proper health checks and graceful shutdown handling
