modules = ["nodejs-20", "web"]
run = "bash start-dev-clean.sh"

hidden = [".config", ".git", "generated-icon.png", "node_modules", "dist"]

[nix]
channel = "stable-24_05"
packages = ["supabase-cli"]

[env]
PORT = "5000"
FRONTEND_PORT = "80"
VITE_API_BASE_URL = "https://chewy-ai.replit.app/api"

[deployment]
deploymentTarget = "autoscale"
build = ["sh", "-c", "npm run build"]
run = ["sh", "-c", "NODE_ENV=production npm run start"]
healthcheck = "/health"

[deployment.env]
NODE_ENV = "production"
PORT = "5000"
FRONTEND_PORT = "80"
BACKEND_URL = "http://localhost:5000"
VITE_API_BASE_URL = "https://chewy-ai.replit.app/api"

[[ports]]
localPort = 80
externalPort = 3000

[[ports]]
localPort = 3000
externalPort = 80

[[ports]]
localPort = 5000
externalPort = 5000

[[ports]]
localPort = 24678

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "bash start-dev-clean.sh"
waitForPort = 5000
